### headless-chat SPIs
com.tencent.supersonic.headless.chat.mapper.SchemaMapper=\
    com.tencent.supersonic.headless.chat.mapper.EmbeddingMapper, \
    com.tencent.supersonic.headless.chat.mapper.KeywordMapper, \
    com.tencent.supersonic.headless.chat.mapper.QueryFilterMapper, \
    com.tencent.supersonic.headless.chat.mapper.TimeFieldMapper,\
    com.tencent.supersonic.headless.chat.mapper.TermDescMapper,\
    com.tencent.supersonic.headless.chat.mapper.LLMPickerSchemaMapper
com.tencent.supersonic.headless.chat.parser.SemanticParser=\
    com.tencent.supersonic.headless.chat.parser.llm.LLMSqlParser,\
    com.tencent.supersonic.headless.chat.parser.rule.RuleSqlParser,\
    com.tencent.supersonic.headless.chat.parser.QueryTypeParser
com.tencent.supersonic.headless.chat.corrector.SemanticCorrector=\
    com.tencent.supersonic.headless.chat.corrector.RuleSqlCorrector,\
    com.tencent.supersonic.headless.chat.corrector.LLMSqlCorrector
com.tencent.supersonic.headless.chat.knowledge.file.FileHandler=\
    com.tencent.supersonic.headless.chat.knowledge.file.FileHandlerImpl
com.tencent.supersonic.headless.chat.parser.llm.DataSetResolver=\
  com.tencent.supersonic.headless.chat.parser.llm.HeuristicDataSetResolver
### headless-core SPIs
com.tencent.supersonic.headless.core.translator.converter.QueryConverter=\
    com.tencent.supersonic.headless.core.translator.converter.DefaultDimValueConverter,\
    com.tencent.supersonic.headless.core.translator.converter.SqlVariableConverter,\
    com.tencent.supersonic.headless.core.translator.converter.SqlQueryConverter,\
    com.tencent.supersonic.headless.core.translator.converter.StructQueryConverter,\
    com.tencent.supersonic.headless.core.translator.converter.MetricRatioConverter
com.tencent.supersonic.headless.core.translator.optimizer.QueryOptimizer=\
    com.tencent.supersonic.headless.core.translator.optimizer.DetailQueryOptimizer,\
    com.tencent.supersonic.headless.core.translator.optimizer.DbDialectOptimizer,\
    com.tencent.supersonic.headless.core.translator.optimizer.ResultLimitOptimizer
com.tencent.supersonic.headless.core.translator.parser.QueryParser=\
    com.tencent.supersonic.headless.core.translator.parser.calcite.CalciteQueryParser
com.tencent.supersonic.headless.core.executor.QueryExecutor=\
    com.tencent.supersonic.headless.core.executor.JdbcExecutor
com.tencent.supersonic.headless.core.cache.QueryCache=\
    com.tencent.supersonic.headless.core.cache.DefaultQueryCache
### headless-server SPIs
com.tencent.supersonic.headless.server.modeller.SemanticModeller=\
    com.tencent.supersonic.headless.server.modeller.RuleSemanticModeller, \
    com.tencent.supersonic.headless.server.modeller.LLMSemanticModeller
### chat-server SPIs
com.tencent.supersonic.chat.server.parser.ChatQueryParser=\
    com.tencent.supersonic.chat.server.parser.NL2PluginParser, \
    com.tencent.supersonic.chat.server.parser.NL2SQLParser,\
    com.tencent.supersonic.chat.server.parser.PlainTextParser
com.tencent.supersonic.chat.server.executor.ChatQueryExecutor=\
    com.tencent.supersonic.chat.server.executor.PluginExecutor, \
    com.tencent.supersonic.chat.server.executor.SqlExecutor,\
    com.tencent.supersonic.chat.server.executor.PlainTextExecutor
com.tencent.supersonic.chat.server.plugin.recognize.PluginRecognizer=\
    com.tencent.supersonic.chat.server.plugin.recognize.embedding.EmbeddingRecallRecognizer
com.tencent.supersonic.chat.server.processor.parse.ParseResultProcessor=\
    com.tencent.supersonic.chat.server.processor.parse.QueryRecommendProcessor,\
    com.tencent.supersonic.chat.server.processor.parse.TimeCostCalcProcessor,\
    com.tencent.supersonic.chat.server.processor.parse.ErrorMsgRewriteProcessor,\
    com.tencent.supersonic.chat.server.processor.parse.ParseInfoFormatProcessor
com.tencent.supersonic.chat.server.processor.execute.ExecuteResultProcessor=\
    com.tencent.supersonic.chat.server.processor.execute.MetricRecommendProcessor,\
    com.tencent.supersonic.chat.server.processor.execute.DimensionRecommendProcessor,\
    com.tencent.supersonic.chat.server.processor.execute.MetricRatioCalcProcessor,\
    com.tencent.supersonic.chat.server.processor.execute.DataInterpretProcessor
### auth-authentication SPIs
com.tencent.supersonic.auth.authentication.interceptor.AuthenticationInterceptor=\
    com.tencent.supersonic.auth.authentication.interceptor.DefaultAuthenticationInterceptor
com.tencent.supersonic.auth.api.authentication.adaptor.UserAdaptor=\
    com.tencent.supersonic.auth.authentication.adaptor.DefaultUserAdaptor
