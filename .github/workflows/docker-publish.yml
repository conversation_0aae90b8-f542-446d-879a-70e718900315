name: Docker Publish

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version of the Docker image'
        required: true
        default: 'latest'

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and publish Docker image
        run: |
          VERSION=${{ github.event.inputs.version }}
          chmod +x docker/docker-build.sh
          chmod +x docker/docker-publish.sh
          sh docker/docker-build.sh $VERSION
          sh docker/docker-publish.sh $VERSION