{"name": "supersonic-chat-sdk", "version": "0.0.0", "main": "dist/index.es.js", "module": "dist/index.es.js", "unpkg": "dist/index.umd.js", "types": "dist/index.d.ts", "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/pro-components": "2.7.0", "@ant-design/x": "^1.4.0", "@codemirror/lang-sql": "^6.9.0", "@rollup/plugin-image": "^3.0.3", "@uiw/react-codemirror": "^4.23.13", "@uiw/react-watermark": "^0.0.5", "ahooks": "^3.8.4", "antd": "^5.24.6", "axios": "^0.21.4", "classnames": "^2.5.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "lodash": "^4.17.21", "moment": "^2.30.1", "rc-animate": "^3.1.1", "rc-queue-anim": "^2.0.0", "rc-texty": "^0.2.0", "rc-tween-one": "^3.0.6", "react-copy-to-clipboard": "^5.1.0", "react-grid-layout": "^1.5.1", "react-json-view": "^1.21.3", "react-markdown": "^9.1.0", "react-spinners": "^0.13.8", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sql-formatter": "^15.5.2", "tslib": "^2.8.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "scripts": {"start": "npm run start:dev", "start:dev": "node scripts/start.js", "watch": "rollup -c rollup/rollup.esm.config.mjs --watch", "watch:bg": "rollup -c rollup/rollup.esm.config.mjs --watch &", "clean": "rimraf ./dist", "build": "npm run clean && npm run build-es", "test": "node scripts/test.js", "build-ts": "tsc -p tsconfig.build.json", "build-css": "lessc ./src/styles/index.less ./dist/index.css", "build-es": "rollup --config rollup/rollup.esm.config.mjs", "build-umd": "rollup --config rollup/rollup.umd.config.mjs"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"react-hooks/exhaustive-deps": 0}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "defaults", "not ie < 8", "last 2 versions", "> 1%", "iOS 7", "last 3 iOS versions"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.10", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.16", "@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-terser": "^0.4.4", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.16", "@types/node": "^16.18.126", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "autoprefixer": "^10.4.21", "babel-jest": "^27.5.1", "babel-loader": "^8.4.1", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.1.0", "bfj": "^7.1.0", "browserslist": "^4.24.4", "camelcase": "^6.3.0", "case-sensitive-paths-webpack-plugin": "^2.4.0", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^3.4.1", "cssnano": "^6.1.2", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.2.0", "file-loader": "^6.2.0", "fs-extra": "^10.1.0", "html-webpack-plugin": "^5.6.3", "http-proxy-middleware": "^2.0.7", "identity-obj-proxy": "^3.0.0", "jest": "^27.5.1", "jest-resolve": "^27.5.1", "jest-watch-typeahead": "^1.1.0", "less": "^4.3.0", "less-loader": "^11.1.4", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-modules": "^6.0.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.8.3", "prompts": "^2.4.2", "react": "^18.3.1", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dom": "^18.3.1", "react-refresh": "^0.11.0", "resolve": "^1.22.10", "resolve-url-loader": "^4.0.0", "rimraf": "^5.0.10", "rollup": "^3.29.5", "rollup-plugin-exclude-dependencies-from-bundle": "^1.1.24", "rollup-plugin-less": "^1.1.3", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-typescript2": "^0.36.0", "sass-loader": "^12.6.0", "semver": "^7.7.1", "source-map-loader": "^3.0.2", "style-loader": "^3.3.4", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "^5.3.14", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "webpack": "^5.99.5", "webpack-dev-server": "^4.15.2", "webpack-manifest-plugin": "^4.1.1", "workbox-webpack-plugin": "^6.6.0"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}, "engines": {"node": ">=16"}}