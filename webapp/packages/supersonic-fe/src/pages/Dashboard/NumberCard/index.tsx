import React from 'react';
import styles from "./style.less";
import NumberCardIcon from "@/assets/images/numberCardIcon.png"

type Props = {
  title?: any,
  msg: string
};

const NumberCard: React.FC<Props> = ({title, msg}) => {
  return (
    <div className={styles.dashboardNumberCard}>
      <div className={styles.dashboardNumberCardHeader}>
        <img src={NumberCardIcon} style={{width: "1rem", height: "1rem"}} alt=""/>
        <div className={styles.dashboardNumberCardHeaderTitle}>{title}</div>
      </div>
      <div className={styles.dashboardNumberCardBody}>
        {msg}
      </div>
    </div>
  );
};

export default NumberCard;
