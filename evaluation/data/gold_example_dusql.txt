SELECT T3.company_name, T3.annual_turnover, T2.brand_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id	internet
SELECT T3.company_name, T2.brand_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id	internet
SELECT T3.company_name, T2.brand_name, T2.legal_representative, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id	internet
SELECT T3.company_name, T3.headquarter_address, T2.brand_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id	internet
SELECT T3.company_name, T2.brand_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id WHERE T1.profit_proportion <= 0.1	internet
SELECT T3.company_name, T2.brand_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id WHERE T1.profit_proportion < 0.1	internet
SELECT T3.company_name, T2.brand_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id WHERE T1.profit_proportion > 0.1	internet
SELECT T2.brand_name, T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital >= ********* GROUP BY T1.brand_id ORDER BY avg(T1.revenue_proportion) DESC LIMIT 1	internet
SELECT T2.brand_name, T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital > ********* GROUP BY T1.brand_id ORDER BY count(*) ASC LIMIT 5	internet
SELECT T2.brand_name, avg(T1.revenue_proportion), T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital < ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, avg(T1.revenue_proportion), T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital >= ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, sum(T1.revenue_proportion), T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, max(T1.revenue_proportion), T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital < ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, max(T1.revenue_proportion), T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital > ********* GROUP BY T1.brand_id HAVING avg(T1.revenue_proportion) = 0.5	internet
SELECT T2.brand_name, T2.legal_representative FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id HAVING count(*) = 5	internet
SELECT T2.brand_name, avg(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id HAVING avg(T1.expenditure_proportion) <= 0.45	internet
SELECT T2.brand_name, min(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital < ********* GROUP BY T1.brand_id HAVING count(*) <= 5	internet
SELECT T2.legal_representative, T2.brand_name, avg(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.legal_representative, T2.brand_name, min(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.legal_representative, T2.brand_name, sum(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.legal_representative, T2.brand_name, max(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.legal_representative, T2.brand_name FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id HAVING count(*) <= 5	internet
SELECT T2.legal_representative, T2.brand_name FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id HAVING avg(T1.revenue_proportion) > 0.5	internet
SELECT T2.brand_name, avg(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id HAVING avg(T1.profit_proportion) <= 0.6	internet
SELECT T2.brand_name, min(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id HAVING count(*) = 5	internet
SELECT T2.brand_name, T2.legal_representative, avg(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id ORDER BY avg(T1.profit_proportion) DESC LIMIT 1	internet
SELECT T2.brand_name, T2.legal_representative, sum(T1.revenue_proportion) FROM company_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id ORDER BY count(*) DESC LIMIT 3	internet
SELECT T3.company_name, T2.brand_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id ORDER BY T1.profit_proportion ASC	internet
SELECT T2.brand_name, T3.company_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id ORDER BY T1.expenditure_proportion ASC LIMIT 3	internet
SELECT T2.brand_name, T3.company_name, T1.revenue_proportion, T1.profit_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id ORDER BY T1.expenditure_proportion DESC LIMIT 3	internet
SELECT T2.brand_name, T3.company_name, T1.revenue_proportion FROM company_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T1.company_id = T3.company_id ORDER BY T1.profit_proportion DESC LIMIT 3	internet
SELECT brand_name FROM brand WHERE legal_representative NOT IN (SELECT legal_representative FROM brand GROUP BY legal_representative HAVING avg(registered_capital) < 1000000)	internet
SELECT T1.brand_name, T2.company_name, T1.legal_representative, T2.annual_turnover FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id	internet
SELECT T1.brand_name, T2.company_name, T1.registered_capital FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id	internet
SELECT T1.brand_name, T2.company_name, T1.registered_capital, T2.annual_turnover FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id	internet
SELECT T1.brand_name, T2.company_name, T1.registered_capital, T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id	internet
SELECT T1.brand_name, T2.company_name, T1.legal_representative, T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id	internet
SELECT T2.company_name, T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id	internet
SELECT T2.company_name, T2.annual_turnover FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital >= *********	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital < *********	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital > *********	internet
SELECT T2.company_name, T2.annual_turnover, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital <= *********	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital > ********* AND T2.annual_turnover <= 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital > ********* AND T2.annual_turnover < 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital < ********* AND T2.annual_turnover < 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital <= ********* AND T2.annual_turnover >= 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital >= ********* AND T2.annual_turnover > 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital <= ********* AND T2.annual_turnover > 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital > ********* AND T2.annual_turnover > 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital <= ********* AND T2.annual_turnover < 28800000000	internet
SELECT T2.company_name, T2.headquarter_address, T1.legal_representative FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T1.registered_capital < ********* AND T2.annual_turnover <= 28800000000	internet
SELECT T3.company_name, T2.brand_name, T1.revenue FROM company_brand_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T2.company_id = T3.company_id WHERE T1.profit <= 50000000000	internet
SELECT T3.company_name, T2.brand_name, T1.revenue FROM company_brand_revenue AS T1 JOIN brand AS T2 JOIN company AS T3 ON T1.brand_id = T2.brand_id AND T2.company_id = T3.company_id WHERE T1.revenue_growth_year_on_year >= 1	internet
SELECT T2.company_name, T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover >= 28800000000 GROUP BY T1.company_id ORDER BY avg(T1.revenue_proportion) ASC LIMIT 5	internet
SELECT T2.company_name, T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover >= 28800000000 GROUP BY T1.company_id ORDER BY avg(T1.revenue_proportion) DESC LIMIT 1	internet
SELECT T2.brand_name, T2.legal_representative FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital > ********* GROUP BY T1.brand_id ORDER BY avg(T1.revenue) DESC LIMIT 1	internet
SELECT T2.brand_name, T2.legal_representative FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id ORDER BY avg(T1.revenue) DESC LIMIT 1	internet
SELECT T2.company_name, T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover >= 28800000000 GROUP BY T1.company_id ORDER BY avg(T1.registered_capital) DESC LIMIT 5	internet
SELECT T2.company_name, T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover >= 28800000000 GROUP BY T1.company_id ORDER BY sum(T1.registered_capital) ASC LIMIT 5	internet
SELECT T2.company_name, max(T1.revenue_proportion), T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover >= 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, sum(T1.revenue_proportion), T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover < 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, max(T1.revenue_proportion), T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover > 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, sum(T1.revenue_proportion), T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover >= 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, avg(T1.revenue_proportion), T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover <= 28800000000 GROUP BY T1.company_id	internet
SELECT T2.brand_name, avg(T1.revenue), T2.legal_representative FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, avg(T1.revenue), T2.legal_representative FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital >= ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, min(T1.revenue), T2.legal_representative FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital < ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, max(T1.revenue), T2.legal_representative FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id	internet
SELECT T2.brand_name, avg(T1.revenue), T2.legal_representative FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital < ********* GROUP BY T1.brand_id	internet
SELECT T2.company_name, min(T1.registered_capital), T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover > 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, min(T1.registered_capital), T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover <= 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, avg(T1.registered_capital), T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover <= 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, max(T1.registered_capital), T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover <= 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, sum(T1.registered_capital), T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover > 28800000000 GROUP BY T1.company_id	internet
SELECT T2.company_name, T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover <= 28800000000 GROUP BY T1.company_id HAVING sum(T1.revenue_proportion) <= 0.5	internet
SELECT T2.company_name, T2.headquarter_address FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover < 28800000000 GROUP BY T1.company_id HAVING sum(T1.revenue_proportion) <= 0.5	internet
SELECT T2.company_name, T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover >= 28800000000 GROUP BY T1.company_id HAVING count(*) <= 5	internet
SELECT T2.company_name, T2.headquarter_address FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover <= 28800000000 GROUP BY T1.company_id HAVING count(*) < 5	internet
SELECT T2.company_name, min(T1.revenue_proportion) FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover > 28800000000 GROUP BY T1.company_id HAVING count(*) > 5	internet
SELECT T2.company_name, max(T1.revenue_proportion) FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover < 28800000000 GROUP BY T1.company_id HAVING avg(T1.profit_proportion) > 0.75	internet
SELECT T2.brand_name, max(T1.revenue) FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital <= ********* GROUP BY T1.brand_id HAVING sum(T1.profit_growth_year_on_year) <= 1000000	internet
SELECT T2.brand_name, min(T1.revenue) FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id WHERE T2.registered_capital < ********* GROUP BY T1.brand_id HAVING count(*) < 5	internet
SELECT T2.company_name, sum(T1.registered_capital) FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover > 28800000000 GROUP BY T1.company_id HAVING count(*) < 5	internet
SELECT T2.company_name, sum(T1.registered_capital) FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id WHERE T2.annual_turnover < 28800000000 GROUP BY T1.company_id HAVING count(*) >= 5	internet
SELECT T2.headquarter_address, T2.company_name, max(T1.revenue_proportion) FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id GROUP BY T1.company_id	internet
SELECT T2.headquarter_address, T2.company_name, min(T1.revenue_proportion) FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id GROUP BY T1.company_id	internet
SELECT T2.headquarter_address, T2.company_name, sum(T1.revenue_proportion) FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id GROUP BY T1.company_id	internet
SELECT T2.headquarter_address, T2.company_name, avg(T1.revenue_proportion) FROM company_revenue AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id GROUP BY T1.company_id	internet
SELECT T2.legal_representative, T2.brand_name, avg(T1.revenue) FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.legal_representative, T2.brand_name, min(T1.revenue) FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.legal_representative, T2.brand_name, sum(T1.revenue) FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.legal_representative, T2.brand_name, max(T1.revenue) FROM company_brand_revenue AS T1 JOIN brand AS T2 ON T1.brand_id = T2.brand_id GROUP BY T1.brand_id	internet
SELECT T2.headquarter_address, T2.company_name, sum(T1.registered_capital) FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id GROUP BY T1.company_id	internet
SELECT T2.headquarter_address, T2.company_name, max(T1.registered_capital) FROM brand AS T1 JOIN company AS T2 ON T1.company_id = T2.company_id GROUP BY T1.company_id	internet
select T2.headquarter_address , T2.company_name, min(T1.registered_capital) from brand as T1 join company as T2 on T1.company_id = T2.company_id group by T1.company_id	internet
select T2.headquarter_address , T2.company_name, avg(T1.registered_capital) from brand as T1 join company as T2 on T1.company_id = T2.company_id group by T1.company_id	internet
select T2.headquarter_address , T2.company_name from company_revenue as T1 join company as T2 on T1.company_id = T2.company_id group by T1.company_id having sum(T1.revenue_proportion) > 0.5	internet
select T2.headquarter_address , T2.company_name from company_revenue as T1 join company as T2 on T1.company_id = T2.company_id group by T1.company_id having count(*) > 5	internet
