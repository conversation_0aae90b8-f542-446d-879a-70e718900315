package com.tencent.supersonic.chat.server.evaluation;

import com.google.common.collect.Lists;
import com.tencent.supersonic.chat.api.pojo.response.QueryResp;
import com.tencent.supersonic.chat.api.pojo.response.QueryResult;
import com.tencent.supersonic.chat.server.agent.Agent;
import com.tencent.supersonic.chat.server.pojo.ChatQueryParam;
import com.tencent.supersonic.chat.server.service.AgentService;
import com.tencent.supersonic.chat.server.service.ChatManageService;
import com.tencent.supersonic.chat.server.service.MemoriesService;
import com.tencent.supersonic.common.config.PromptLoader;
import com.tencent.supersonic.common.pojo.ChatApp;
import com.tencent.supersonic.common.util.ContextUtils;
import com.tencent.supersonic.common.util.JsonUtil;
import com.tencent.supersonic.headless.api.pojo.response.QueryState;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 事实性内存评估组件
 *
 * <AUTHOR>
 * @date 2025/4/27
 **/

@ConditionalOnProperty(prefix = "factual.evaluator", name = "enabled", havingValue = "true")
@Component
@Slf4j
public class FactualMemoryEvaluator {
    //    private static final int INTERVAL_MINUTES = 30;
    private static final boolean USE_SYSTEM_MESSAGE = false;
    // 先使用 RewriteMultiTurn的Key
    private static final String APP_KEY = "REWRITE_MULTI_TURN";

    @Autowired
    private ChatManageService chatManageService;
    @Autowired
    private MemoriesService memoriesService;
    @Autowired
    private FactualEmbeddingHelper factualEmbeddingHelper;
    @Autowired
    private EvaluationModelHandler modelHandler;

    @Value("${factual.evaluator.interval-minutes:30}")
    private int intervalMinutes;


    @Async
    @Scheduled(fixedDelayString = "#{${factual.evaluator.interval-minutes:30} * 60 * 1000}")
    public void execute() {
        // 因为定时任务内天都执行，所以只考虑过于24h到现在的数据
        var endAt = LocalDateTime.now();
        var startAt = endAt.minusMinutes(intervalMinutes);
        log.info("[FactualEval] 开始处理过去{}分钟内的聊天记录，从 {} 到 {}", intervalMinutes, startAt, endAt);

        ChatQueryParam queryParam = new ChatQueryParam().setStartAt(startAt).setEndAt(endAt);

        List<QueryResp> histQueries = chatManageService.getChatQueries(queryParam).stream()
                .filter(q -> Objects.nonNull(q.getQueryResult())
                        && q.getQueryResult().getQueryState() == QueryState.SUCCESS)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(histQueries)) {
            log.debug("[FactualEval] 没有找到需要处理的聊天记录");
            return;
        }

        log.debug("[FactualEval] 找到 {} 条聊天记录待处理", histQueries.size());

        // 按chatId分组处理
        Map<Long, List<QueryResp>> chatGroups = histQueries.stream()
                .collect(Collectors.groupingBy(QueryResp::getChatId));

        List<FactualEvalResult> evalResults = new ArrayList<>();

        chatGroups.forEach((chatId, queries) -> {
            log.info("[FactualEval] 开始处理chatId={}的记录，共 {} 条", chatId, queries.size());
            if (CollectionUtils.isNotEmpty(queries)) {
                Integer agentId = queries.get(0).getAgentId();
                List<FactualEvalResult> results = processChat(agentId, chatId, queries);
                evalResults.addAll(results);
            }
        });
        saveEvalResult(evalResults);
        log.info("[FactualEval] 处理完成");
    }

    private void saveEvalResult(List<FactualEvalResult> evalResults) {
        if (CollectionUtils.isEmpty(evalResults)) {
            log.debug("[FactualEval] 没有有效的评估结果需要保存");
            return;
        }
        List<FactualEvalResult> validResults = evalResults.stream()
                .filter(it -> Objects.nonNull(it.getScore()) && it.getScore() > 0)
                .collect(Collectors.toList());

        var resultGroup = validResults.stream().collect(Collectors.groupingBy(FactualEvalResult::getAgentId));
        resultGroup.forEach((agentId, results) -> {
            if (CollectionUtils.isEmpty(results)) {
                log.debug("[FactualEval] agentId={} 没有有效的评估结果需要保存", agentId);
                return;
            }
            log.debug("[FactualEval] agentId={} 开始写入 {} 条factual记录", agentId, results.size());
            factualEmbeddingHelper.addFactualMemory(results, agentId);
            memoriesService.batchAddMemory(results);
            log.debug("[FactualEval] agentId={} 成功写入 {} 条factual记录", agentId, results.size());
        });
    }

    private List<ChatMessage> buildModelContexts(List<QueryResp> historyQueries) {
        return historyQueries.stream()
                .flatMap(hist -> Stream.of(
                        new UserMessage(hist.getQueryText()),
                        new AiMessage(buildAiMessageContent(hist))
                ))
                .collect(Collectors.toList());
    }


    private String buildAiMessageContent(QueryResp hist) {
        QueryResult queryResult = hist.getQueryResult();
        return StringUtils.isBlank(queryResult.getTextSummary())
                ? queryResult.getTextResult()
                : queryResult.getTextSummary();
    }


    private List<FactualEvalResult> processChat(Integer agentId, Long chatId, List<QueryResp> hists) {
        if (CollectionUtils.isEmpty(hists)) {
            return Collections.emptyList();
        }
        List<FactualEvalResult> evalResults = callModel(agentId, hists);

        if (CollectionUtils.isEmpty(evalResults)) {
            return Collections.emptyList();
        }

        // 设置评估结果的元数据
        evalResults.forEach(evalResult -> {
            evalResult.setAgentId(agentId).setChatId(chatId);
        });
        return evalResults;
    }

    private List<FactualEvalResult> callModel(Integer agentId, List<QueryResp> hists) {
        ChatApp chatApp = getChatApp(agentId);
        if (Objects.isNull(chatApp)) {
            return Collections.emptyList();
        }

        List<ChatMessage> messages = Lists.newArrayList();

        if (USE_SYSTEM_MESSAGE) {
            String evalPrompt = PromptLoader.getPrompt("factual_eval_hist");
            SystemMessage systemMessage = new SystemMessage(evalPrompt);
            messages.add(systemMessage);
        }

        List<ChatMessage> contexts = buildModelContexts(hists);
        messages.addAll(contexts);

        if (USE_SYSTEM_MESSAGE) {
            messages.add(new UserMessage(
                    "analyze the user messages in the historical conversations and analyze whether the user messages are factual statements in terms of semantics and intent."));
        } else {
            String template = PromptLoader.getPrompt("factual_eval_hist_user_template");
            messages.add(new UserMessage(template));
        }

        String modelResponse = modelHandler.generate(messages, chatApp.getChatModelConfig());

        if (StringUtils.isBlank(modelResponse)) {
            log.debug("[FactualEval] 模型返回内容为空");
            return Collections.emptyList();
        }
        if (JsonUtil.isJson(modelResponse)) {
            return JsonUtil.toList(modelResponse, FactualEvalResult.class);
        }
        log.warn("[FactualEval] 模型返回内容不是有效的JSON格式: {}", modelResponse);
        return Collections.emptyList();
    }

    private static ChatApp getChatApp(Integer agentId) {
        AgentService agentService = ContextUtils.getBean(AgentService.class);
        Agent agent = agentService.getAgent(agentId);
        ChatApp chatApp = agent.getChatAppConfig().get(APP_KEY);
        if (Objects.isNull(chatApp) || !chatApp.isEnable()) {
            return null;
        }
        return chatApp;
    }
}
