.nextChatMobileTableBox {
 .mobileTableMoreModelBox {
  position: fixed;
  z-index: 9999;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);

  .mobileTableMoreModel {
   position: absolute;
   z-index: 9999;
   left: 0;
   right: 0;
   bottom: 0;
   height: 90vh;
   background-color: white;
   border-radius: 1rem 1rem 0 0;

   .mobileTableMoreModelBody {
    padding: 1rem;
    height: 80vh;
    overflow-y: auto;
   }

   .mobileTableMoreModelHeader {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #eeeeee;

    .mobileTableMoreModelClose {
     font-size: 1.3rem;
     color: #6B758B;
    }
   }
  }
 }

 .mobileTableBoxMoreBtn {
  text-align: right;
  color: #670505;
  margin-bottom: 0.5rem;
 }

}
