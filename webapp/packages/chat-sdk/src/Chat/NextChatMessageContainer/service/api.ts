import axios from '../../../service/axiosInstance';


export function addExampleQuery(
    params: { name: string, sql: string, usageGuidance?: string },
) {
    return axios.post<any>(`/api/v2/semantic/example-query`, params);
}

export function addSpecialExampleQuery(
    params: { name: string, sql: string, parameters?: any },
) {
    return axios.post<any>(`/api/v2/semantic/parameterized-example-query`, params);
}
