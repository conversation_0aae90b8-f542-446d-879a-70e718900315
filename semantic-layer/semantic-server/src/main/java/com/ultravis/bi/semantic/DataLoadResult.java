package com.ultravis.bi.semantic;

import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Accessors(chain = true)
@Data
@NoArgsConstructor
public class DataLoadResult {
    private ParseResult parseResult;
    private List<Map<String, Object>> data;
    private List<PostDataLoadProcessor.RelatedViewQueryGuidance> relatedViewQueryGuidance;
}
