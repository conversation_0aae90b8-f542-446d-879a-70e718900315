import React, {useEffect, useState} from 'react';
import {Form, Input, message, Modal} from 'antd';
import {AgentMessageInfo} from "../data";
import TextArea from "antd/es/input/TextArea";
import {addExampleQuery} from "../service/api";
import {sql} from "@codemirror/lang-sql";
import CodeMirror from "@uiw/react-codemirror/esm";

type Props = {
    open: boolean;
    question: string;
    chatMessage: AgentMessageInfo
    onCloseExampleModal: () => void;
};

const ExampleModal: React.FC<Props> = ({
                                           open = false,
                                           question,
                                           chatMessage,
                                           onCloseExampleModal
                                       }) => {

        const [form] = Form.useForm();

        const [confirmLoading, setConfirmLoading] = useState(false);

        useEffect(() => {
            form.setFieldValue("name", question);
            const inputSql = chatMessage?.parseResult?.inputSql || ""
            form.setFieldValue("sql", inputSql);
        }, [question, chatMessage]);

        const handleOk = () => {
            setConfirmLoading(true);
            form
                .validateFields({validateOnly: true})
                .then(() => {
                    const params = form.getFieldsValue()
                    addExampleQuery(params).then(() => {
                        message.success("添加成功");
                        setConfirmLoading(false)
                        const timer = setTimeout(() => {
                            onCloseExampleModal()
                            clearTimeout(timer)
                        }, 500)
                    })
                })
                .catch(() => setConfirmLoading(false));
        };

        const handleCancel = () => {
            console.log('Clicked cancel button');
            onCloseExampleModal();
        };

        return (
            <>
                <Modal
                    title="添加示例"
                    width="60vw"
                    open={open}
                    onOk={handleOk}
                    confirmLoading={confirmLoading}
                    onCancel={handleCancel}
                >
                    <Form form={form} name="validateOnly" layout="vertical" autoComplete="off">
                        <Form.Item name="name" label="查询内容" rules={[{required: true}]}>
                            <Input/>
                        </Form.Item>
                        <Form.Item name="sql" label="大模型SQL" rules={[{required: true}]}>
                            <CodeMirror height="200px" extensions={[sql()]}/>
                        </Form.Item>
                        <Form.Item name="usageGuidance" label="适用范围">
                            <TextArea/>
                        </Form.Item>
                    </Form>
                </Modal>
            </>
        );
    }
;

export default ExampleModal;
