import {forwardRef, ForwardRefRenderFunction, useEffect, useRef, useState,} from 'react';
import styles from './style.module.less';
import {Button, ConfigProvider, Divider, Dropdown, Flex, GetRef, MenuProps, message, theme} from 'antd';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import {Sender} from "@ant-design/x";
import {AudioOutlined, CodeSandboxOutlined, PlusOutlined} from "@ant-design/icons";

dayjs.locale('zh-cn');

type Props = {
    loading: boolean;
    showIot: boolean;
    showRag: boolean;
    useRag: boolean;
    useNewVersion: boolean;
    showNewVersion: boolean;
    focus?: boolean;
    customMsg?: string;
    onSubmit: (_content: string) => void
    useIot: boolean
    onOpenIot?: (_flag: boolean) => void
    onUseRag?: (_flag: boolean) => void
    onUseNewVersion?: (_flag: boolean) => void
    onOpenVoiceInput: () => void
    onChangeChatPerformanceMode: (model: 'speed-first' | 'quality-first') => void;
};

const MobileChatFooter: ForwardRefRenderFunction<any, Props> = ({
                                                                    loading,
                                                                    showIot,
                                                                    showRag,
                                                                    useRag,
                                                                    useNewVersion,
                                                                    showNewVersion,
                                                                    focus,
                                                                    customMsg,
                                                                    onSubmit,
                                                                    useIot,
                                                                    onOpenIot,
                                                                    onUseRag,
                                                                    onUseNewVersion,
                                                                    onOpenVoiceInput,
                                                                    onChangeChatPerformanceMode
                                                                }) => {
    const {token} = theme.useToken();
    const iconStyle = {
        fontSize: 18,
        color: token.colorText,
    };
    const senderRef = useRef<GetRef<typeof Sender>>(null);


    const [inputValue, setInputValue] = useState('')

    useEffect(() => {
        if (customMsg) {
            setInputValue(customMsg)
        }
    }, [customMsg]);

    useEffect(() => {
        if (focus) {
            senderRef.current!.focus({cursor: 'all',});
        }
    }, [focus]);

    const handlerOpenSearchOnline = () => {
        const flag = !useIot
        onOpenIot?.(flag)
    }

    const handlerUseRag = () => {
        const flag = !useRag
        onUseRag?.(flag)
    }

    const handlerUseNewVersion=()=>{
        const flag = !useNewVersion
        onUseNewVersion?.(flag)
    }

    const handlerOnOpenVoiceInput = async () => {
        try {
            await navigator.mediaDevices.getUserMedia({audio: true});
            onOpenVoiceInput()
        } catch (error) {
            console.log(error)
            message.error("申请麦克风权限被拒绝")
        }
    }

    const items: MenuProps['items'] = [
        {
            key: '1',
            label: "质量优先",
            onClick: () => onChangeChatPerformanceMode("quality-first")
        },
        {
            key: '2',
            label: "速度优先",
            onClick: () => onChangeChatPerformanceMode("speed-first")
        },
    ];

    const createSenderFooterDom = (components: any) => {
        const {SendButton, LoadingButton} = components;
        return (
            <Flex justify="space-between" align="end">
                <Flex align="center">
                    <Dropdown menu={{items}} placement="topLeft" arrow>
                        <Button shape="circle" icon={<PlusOutlined/>}/>
                    </Dropdown>
                    {showIot &&
                        (
                            <>
                                <Divider type="vertical"/>
                                <Button style={{borderRadius: '2rem'}}
                                        icon={<CodeSandboxOutlined/>}
                                        color={useIot ? "primary" : undefined}
                                        variant={useIot ? "filled" : undefined}
                                        onClick={() => handlerOpenSearchOnline()}>
                                    物联控制
                                </Button>
                            </>
                        )
                    }
                    {showRag &&
                        (
                            <>
                                <Divider type="vertical"/>
                                <Button style={{borderRadius: '2rem'}}
                                        icon={<CodeSandboxOutlined/>}
                                        color={useRag ? "primary" : undefined}
                                        variant={useRag ? "filled" : undefined}
                                        onClick={() => handlerUseRag()}>
                                    智能体模式
                                </Button>
                            </>
                        )
                    }
                    {showNewVersion &&
                        (
                            <>
                                <Divider type="vertical"/>
                                <Button style={{borderRadius: '2rem'}}
                                        icon={<CodeSandboxOutlined/>}
                                        color={useNewVersion ? "primary" : undefined}
                                        variant={useNewVersion ? "filled" : undefined}
                                        onClick={() => handlerUseNewVersion()}>
                                    新版
                                </Button>
                            </>
                        )
                    }
                </Flex>
                <Flex align="center">
                    <Button style={iconStyle} type="text" icon={<AudioOutlined/>}
                            onClick={() => handlerOnOpenVoiceInput()}/>
                    <Divider type="vertical"/>
                    {loading ? (
                        <LoadingButton type="default"/>
                    ) : (
                        <SendButton type="primary" disabled={false}/>
                    )}
                </Flex>
            </Flex>
        );
    }

    return (
        <ConfigProvider locale={locale}>
            <div className={styles.mobileChatFooterBox}>
                <Sender
                    ref={senderRef}
                    style={{borderRadius: '2rem 2rem 0 0'}}
                    className={styles.chatSenderBoxChat}
                    value={inputValue}
                    onChange={setInputValue}
                    autoSize={{minRows: 3, maxRows: 10}}
                    placeholder="有什么我能帮您的吗？"
                    actions={false}
                    footer={({components}) => createSenderFooterDom(components)}
                    onSubmit={value => {
                        if (value.trim()) {
                            onSubmit(value)
                            setInputValue('')
                        }
                    }}
                />
            </div>
        </ConfigProvider>
    );
};

export default forwardRef(MobileChatFooter);
