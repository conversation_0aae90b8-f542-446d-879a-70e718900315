package com.ultravis.bi.semantic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tencent.supersonic.headless.api.pojo.*;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.ultravis.bi.infra.SensitiveFieldsRepo;
import com.ultravis.bi.semantic.rewrite.rewriter.ColumnInfo;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import com.ultravis.bi.text2sql.DimensionValueUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * 对加载后的数据进行后处理
 */
public class PostDataLoadProcessor {

    public static final String SUFFIX_SYNTHESIZED_DESCRIPTION = "_synthesized_description";

    public static void addDescriptionColumns(SemanticSchema semanticSchema, List<ModelResp> models, DataLoadResult result) {
        Map<String, Map<String, List<String>>> dimensionIdToValueMapping = DimensionValueUtil.getValueMappingsOfDimension(semanticSchema, models);

        List<ColumnInfo> dataColumns = result.getParseResult().getColumns();
        Map<String, Map<String, List<String>>> columnNameToValueMapping =
                getColumnNameToValueMapping(dataColumns, dimensionIdToValueMapping);

        // 插入描述性字段
        for (int i = dataColumns.size() - 1; i >= 0; i--) {
            ColumnInfo columnInfo = dataColumns.get(i);
            if (!columnInfo.isField() || !columnNameToValueMapping.containsKey(columnInfo.getName())) {
                continue;
            }
            ColumnInfo descriptionColumn = new ColumnInfo()
                    .setName(getDescriptionColumnName(columnInfo.getName()))
                    .setDescription(getDescriptionColumnDescription(columnInfo.getDescription()));
            dataColumns.add(i + 1, descriptionColumn);
        }

        result.getData().forEach(row -> {
            List<String> cols = row.keySet().stream().toList();
            cols.forEach(col -> {
                if (!columnNameToValueMapping.containsKey(col)) {
                    return;
                }
                Object cell = row.get(col);
                if (cell == null) {
                    return;
                }
                Map<String, List<String>> nativeValueToSemanticValues = columnNameToValueMapping.get(col);
                List<String> semanticValues = nativeValueToSemanticValues.get(cell.toString());
                if (semanticValues != null && !semanticValues.isEmpty()) {
                    row.put(getDescriptionColumnName(col), semanticValues.getFirst());
                }
            });
        });
    }

    public static void addColumnMetaInfo(List<ModelResp> models, DataLoadResult result) {
        result.getParseResult().getColumns().stream()
                .filter(ColumnInfo::isField)
                .forEach(col -> setColumnMetaInfo(models, col));
    }

    public static void maskSensitiveFields(DataLoadResult result) {
        List<ColumnInfo> columns = result.getParseResult().getColumns();

        List<String> presentedModels = columns.stream()
                .filter(ColumnInfo::isField)
                .map(ColumnInfo::getOriginModelName)
                .distinct()
                .toList();
        List<SensitiveField> sensitiveFields = SensitiveFieldsRepo.listByModels(presentedModels);

        columns.stream()
                .map(col -> {
                    return getSensitiveField(col.getOriginModelName(), col.getOriginFieldName())
                            .map(f -> Pair.of(col, f))
                            .orElse(Pair.of(col, null));
                })
                .filter(it -> it.getRight() != null)
                .forEach(it -> {
                    result.getData().forEach(row -> {
                        String name = it.getLeft().getName();
                        Object value = row.get(name);
                        SensitiveField sensitiveField = it.getRight();
                        row.put(name, sensitiveField.getProtectionPolicy().apply(value));
                    });
                });
    }

    @NotNull
    private static Optional<SensitiveField> getSensitiveField(String originModelName, String originFieldName) {
        return Optional.empty();
    }

    @Accessors(chain = true)
    @Data
    @NoArgsConstructor
    public static class RelatedViewQueryGuidance {
        String title;
        String description;
        String name;
        String baseModelName;
        @JsonIgnore
        Map<String, List<ColumnInfo>> primaryKeysGroup;
        Map<String, String> requiredParams;
        List<RelatedViewQueryGuidance> children = new ArrayList<>();
    }

    private record Node(String baseModel,
                        Map<String, List<ColumnInfo>> primaryKeysGroup,
                        Map<String, String> requiredParams) {
    }

    public static void addRelatedViewQueryGuidance(ParseSqlContext parseSqlContext, DataLoadResult execSqlResult) {
        Set<String> visited = new HashSet<>();
        Queue<String> queue = new LinkedList<>(visited);
        Map<String, RelatedViewQueryGuidance> nodes = new HashMap<>();
        Map<String, Node> N = new HashMap<>();
        List<RelatedViewQueryGuidance> roots = new ArrayList<>();

        Map<String, List<ColumnInfo>> modelToPrimaryKeys = execSqlResult.getParseResult().getColumns().stream()
                .filter(ColumnInfo::isPrimaryKey)
                .collect(groupingBy(
                        ColumnInfo::getOriginModelName,
                        Collectors.mapping(Function.identity(), Collectors.toList())
                ));

        modelToPrimaryKeys.forEach((modelName, primaryKeys) -> {
            queue.add(modelName);
            visited.add(modelName);

            Node n = new Node(
                    modelName,
                    Map.of(modelName, primaryKeys),
                    primaryKeys.stream().collect(toMap(ColumnInfo::getOriginFieldName, ColumnInfo::getName)));
            N.put(modelName, n);
        });

        List<SemanticRelationship> biDirectionalRelationships = getBiDirectionalRelationships(parseSqlContext);

        while (!queue.isEmpty()) {
            String modelName = queue.poll();

            var n = N.get(modelName);
            Map<String, String> requiredParams = n.requiredParams();

            var children = biDirectionalRelationships.stream()
                    .filter(r -> r.fromModelName().equals(modelName))
                    .filter(r -> !visited.contains(r.toModelName()))
                    .map(rel -> {
                        Optional<RelationshipViewManager.ParsedRelationshipView> viewOpt =
                                RelationshipViewManager.getView(rel.fromModelName(), rel.toModelName(), parseSqlContext);
                        if (viewOpt.isEmpty()) {
                            return null;
                        }
                        RelationshipViewManager.ParsedRelationshipView view = viewOpt.get();
                        return new RelatedViewQueryGuidance()
                                .setName(view.name())
                                .setBaseModelName(rel.toModelName())
                                .setTitle(view.description())
                                .setDescription(view.description())
                                .setPrimaryKeysGroup(view.parseResult().primaryKeysGroup())
                                .setRequiredParams(requiredParams);
                    }).filter(Objects::nonNull).toList();

            RelatedViewQueryGuidance parent = nodes.get(modelName);
            if (parent != null) {
                parent.setChildren(children);
            } else {
                roots.addAll(children);
            }
            children.forEach(it -> {
                nodes.put(it.baseModelName, it);
                visited.add(it.baseModelName);
                queue.add(it.baseModelName);

                Node nd = new Node(
                        it.baseModelName,
                        it.primaryKeysGroup,
                        it.primaryKeysGroup.getOrDefault(it.baseModelName, List.of()).stream()
                                .collect(toMap(ColumnInfo::getOriginFieldName, ColumnInfo::getName)));
                N.put(it.baseModelName, nd);
            });
        }

        execSqlResult.setRelatedViewQueryGuidance(roots);
    }

    private static List<SemanticRelationship> getBiDirectionalRelationships(ParseSqlContext parseSqlContext) {
        List<SemanticRelationship> rel = parseSqlContext.semanticModelsAndRelationships.getRight();
        Stream<SemanticRelationship> backRel = rel.stream().map(r -> {
            return new SemanticRelationship(r.toModelName(), r.fromModelName(), r.joinType(), r.joinCondition());
        });
        return Stream.concat(rel.stream(), backRel).toList();
    }

    private static void setColumnMetaInfo(List<ModelResp> models, ColumnInfo col) {
        String modelName = col.getOriginModelName();
        String dimName = col.getOriginFieldName();

        ModelResp modelResp = models.stream()
                .filter(m -> m.getBizName().equals(modelName))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("模型未找到: " + modelName));
        ModelDetail modelDetail = modelResp.getModelDetail();

        Optional<Dimension> dimension = modelDetail.getDimensions().stream()
                .filter(d -> d.getBizName().equals(dimName))
                .findFirst();
        if (dimension.isPresent()) {
            col.setDescription(dimension.get().getName());
            return;
        }

        Optional<Measure> measure = modelDetail.getMeasures().stream()
                .filter(m -> m.getBizName().equals(dimName))
                .findFirst();
        if (measure.isPresent()) {
            col.setDescription(measure.get().getName());
            return;
        }

        Optional<Identify> identifier = modelDetail.getIdentifiers().stream()
                .filter(id -> id.getBizName().equals(dimName))
                .findFirst();
        if (identifier.isPresent()) {
            col.setDescription(identifier.get().getName());
            return;
        }

        throw new RuntimeException("字段未找到: " + col.getOriginField());
    }

    // {字段名: {字典值: [别名 1, 别名 2, ...]}}
    @NotNull
    private static Map<String, Map<String, List<String>>> getColumnNameToValueMapping(List<ColumnInfo> dataColumns, Map<String, Map<String, List<String>>> dimensionIdToValueMapping) {
        Map<String, Map<String, List<String>>> columnNameToValueMapping = new HashMap<>();
        dataColumns.stream()
                .filter(ColumnInfo::isField)
                .forEach(it -> {
                    Map<String, List<String>> nativeValueToSemanticValues = dimensionIdToValueMapping.get(it.getOriginField());
                    if (nativeValueToSemanticValues != null) {
                        columnNameToValueMapping.put(it.getName(), nativeValueToSemanticValues);
                    }
                });
        return columnNameToValueMapping;
    }

    @NotNull
    private static String getDescriptionColumnName(String name) {
        return name + SUFFIX_SYNTHESIZED_DESCRIPTION;
    }

    private static String getDescriptionColumnDescription(String description) {
        return description + "(描述)";
    }
}
