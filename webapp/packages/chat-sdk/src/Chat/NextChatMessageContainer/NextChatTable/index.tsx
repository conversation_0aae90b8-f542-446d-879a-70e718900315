import React, {useEffect, useState} from 'react';
import styles from './style.module.less';
import {AgentMessageInfo} from "../data";
import {ProTable} from '@ant-design/pro-components';

type Props = {
    agentMessage: AgentMessageInfo;
};

const NextChatTable: React.FC<Props> = ({
                                            agentMessage
                                        }) => {


    const [tableListDataSource, setTableListDataSource] = useState<any>([]);
    const [tableColumns, setTableColumns] = useState<{
        title: string
        dataIndex: string
        key: string
    }[]>([]);

    useEffect(() => {
        console.log("表格数据", agentMessage)
        if (agentMessage) {
            initTableColumns()
        }
    }, [agentMessage]);

    const initTableColumns = () => {
        const parseResult = agentMessage.parseResult;
        const columns = parseResult.columns;
        const tmp = columns.map(item => {
            return {
                title: item.description || item.name,
                dataIndex:  item.name || item.description,
                key: item.name || item.description,
            }
        })
        setTableColumns(tmp);
        setTableListDataSource(agentMessage.data);
    }

    return (
        <div className={styles.nextChatTableBox}>
            <ProTable
                columns={tableColumns}
                dataSource={tableListDataSource}
                rowKey="key"
                search={false}
                dateFormatter="string"
            />
        </div>
    );
};


export default NextChatTable;
