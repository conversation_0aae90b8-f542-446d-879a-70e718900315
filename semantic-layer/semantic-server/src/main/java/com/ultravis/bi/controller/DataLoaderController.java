package com.ultravis.bi.controller;

import com.ultravis.bi.semantic.DataLoadResult;
import com.ultravis.bi.service.DataLoaderService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/v2/semantic")
public class DataLoaderController {

    private final DataLoaderService dataLoaderService;

    public DataLoaderController(DataLoaderService dataLoaderService) {
        this.dataLoaderService = dataLoaderService;
    }

    @PostMapping("/load-data")
    public DataLoadResult loadData(@RequestBody TextToSqlController.LoadDataReq loadDataReq) {
        return dataLoaderService.loadDataBySemanticSql(loadDataReq.sql());
    }

    public record LoadDataByViewReq(String viewName, Map<String, Object> params) {
    }

    @PostMapping("/load-data-by-view")
    public DataLoadResult loadDataByView(@RequestBody LoadDataByViewReq loadDataReq) {
        return dataLoaderService.loadDataByView(loadDataReq.viewName(), loadDataReq.params());
    }
}
