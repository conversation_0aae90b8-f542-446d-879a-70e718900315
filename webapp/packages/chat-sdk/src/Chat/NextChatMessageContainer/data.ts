export interface ParseResultColumn {
    name: string
    originField: string
    agg: string
    description: string
    field: boolean
    aggregation: boolean
}

export interface ParseResult {
    inputSql: string;
    outputSql: string;
    columns: ParseResultColumn[]
}

export interface AgentMessageInfo {
    parseResult: ParseResult;
    data: any
}

export interface ChatMessage {
    id?: string;
    showSql?: boolean;
    currentShowSql?: string;
    role: 'user' | 'agent',
    chatId: string | number,
    messageData: string | AgentMessageInfo,
}

export const makeUUID = () => {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const uuid: string[] = [];
    for (let i = 0; i < 32; i++) {
        uuid[i] = chars[Math.floor(Math.random() * 62)];
    }
    return uuid.join('');
}
