package com.ultravis.bi.agent.graph;

import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.agent.tool.ToolSpecifications;
import dev.langchain4j.data.message.ToolExecutionResultMessage;
import dev.langchain4j.service.tool.DefaultToolExecutor;
import dev.langchain4j.service.tool.ToolExecutor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/5/26
 **/

@Slf4j
public class ToolNode {
    record Specification(ToolSpecification value, ToolExecutor executor) {
        public Specification(Object objectWithTool, Method method) {
            this(ToolSpecifications.toolSpecificationFrom(method),
                    new DefaultToolExecutor(objectWithTool, method));
        }
    }

    public static ToolNode of(Collection<Object> objectsWithTools) {
        List<Specification> toolSpecifications = new ArrayList<>();

        for (Object objectWithTool : objectsWithTools) {
            for (Method method : objectWithTool.getClass().getDeclaredMethods()) {
                if (method.isAnnotationPresent(Tool.class)) {
                    toolSpecifications.add(new Specification(objectWithTool, method));
                }
            }
        }
        return new ToolNode(toolSpecifications);
    }

    public static ToolNode of(Object... objectsWithTools) {
        return of(Arrays.asList(objectsWithTools));
    }

    private final List<Specification> entries;

    private ToolNode(List<Specification> entries) {
        if (entries.isEmpty()) {
            throw new IllegalArgumentException("entries cannot be empty");
        }
        this.entries = entries;
    }

    public List<ToolSpecification> toolSpecifications() {
        return this.entries.stream().map(Specification::value).toList();
    }

    public Optional<ToolExecutionResultMessage> execute(ToolExecutionRequest request,
            Object memoryId) {
        log.trace("execute: {}", request.name());

        return entries.stream().filter(v -> v.value.name().equalsIgnoreCase(request.name()))
                .findFirst().map(e -> {
                    String value = e.executor().execute(request, memoryId);
                    return new ToolExecutionResultMessage(request.id(), request.name(), value);
                });
    }

    public Optional<ToolExecutionResultMessage> execute(Collection<ToolExecutionRequest> requests,
            Object memoryId) {
        for (ToolExecutionRequest request : requests) {
            Optional<ToolExecutionResultMessage> result = execute(request, memoryId);
            if (result.isPresent()) {
                return result;
            }
        }
        return Optional.empty();
    }

    public Optional<ToolExecutionResultMessage> execute(ToolExecutionRequest request) {
        return execute(request, null);
    }

    public Optional<ToolExecutionResultMessage> execute(Collection<ToolExecutionRequest> requests) {
        return execute(requests, null);
    }
}
