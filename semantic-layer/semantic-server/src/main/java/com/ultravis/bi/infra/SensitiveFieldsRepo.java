package com.ultravis.bi.infra;

import com.ultravis.bi.semantic.SensitiveField;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class SensitiveFieldsCatalogRepo {

    public static List<SensitiveField> getSensitiveFields(List<String> modelNames) {
        return List.of(new SensitiveField()
                .setModelName("*")
                .setFieldName("XH")
                .setSensitiveLevel(2)
                .setProtectionPolicy(SensitiveField.ProtectionPolicy.MASK));
    }

}
