package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.common.pojo.ModelRela;
import com.tencent.supersonic.headless.api.pojo.MetaFilter;
import com.tencent.supersonic.headless.api.pojo.SchemaItem;
import com.tencent.supersonic.headless.api.pojo.request.SchemaFilterReq;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.tencent.supersonic.headless.api.pojo.response.SemanticSchemaResp;
import com.tencent.supersonic.headless.server.service.ModelRelaService;
import com.tencent.supersonic.headless.server.service.ModelService;
import com.tencent.supersonic.headless.server.service.SchemaService;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.ultravis.bi.infra.schema.Schemas.toSemanticModels;
import static com.ultravis.bi.infra.schema.Schemas.toSemanticRelationships;

@Repository
public class SchemaRepo {

    private final ModelService modelService;
    private final ModelRelaService modelRelaService;
    private final SchemaService schemaService;

    public SchemaRepo(ModelService modelService, ModelRelaService modelRelaService, SchemaService schemaService) {
        this.modelService = modelService;
        this.modelRelaService = modelRelaService;
        this.schemaService = schemaService;
    }

    public List<SemanticModel> getSemanticModels(long dataSetId) {
        MetaFilter metaFilter = new MetaFilter();
        metaFilter.setDataSetId(dataSetId);
        List<ModelResp> supersonicModels = modelService.getModelList(metaFilter);
        return toSemanticModels(supersonicModels, new OracleColumnTypeToJavaTypeMapper());
    }

    public List<ModelResp> getSupersonicModels(long dataSetId) {
        MetaFilter metaFilter = new MetaFilter();
        metaFilter.setDataSetId(dataSetId);
        return modelService.getModelList(metaFilter);
    }

    public Pair<List<ModelResp>, List<ModelRela>> getSupersonicModelsAndRelationships(long dataSetId) {
        List<ModelResp> supersonicModels = getSupersonicModels(dataSetId);
        List<Long> modelIds = supersonicModels.stream().map(SchemaItem::getId).toList();
        List<ModelRela> relationships = modelRelaService.getModelRela(modelIds);
        return Pair.of(supersonicModels, relationships);
    }

    public Pair<List<SemanticModel>, List<SemanticRelationship>> getSemanticModelsAndRelationships(long dataSetId) {
        List<ModelResp> supersonicModels = getSupersonicModels(dataSetId);
        List<Long> modelIds = supersonicModels.stream().map(SchemaItem::getId).toList();
        List<ModelRela> relationships = modelRelaService.getModelRela(modelIds);
        return Pair.of(toSemanticModels(supersonicModels, new OracleColumnTypeToJavaTypeMapper()), toSemanticRelationships(supersonicModels, relationships));
    }

    public Pair<List<ModelResp>, List<ModelRela>> getSupersonicModelsAndRelationships_old(long dataSetId) {
        var supersonicSemanticSchema = getSupersonicSemanticSchema(dataSetId);

        List<ModelResp> supersonicModels = supersonicSemanticSchema.getModelResps();
        List<ModelRela> relationships = supersonicSemanticSchema.getModelRelas();

        return Pair.of(supersonicModels, relationships);
    }

    public Pair<List<SemanticModel>, List<SemanticRelationship>> getSemanticModelsAndRelationships_old(long dataSetId) {
        SemanticSchemaResp supersonicSemanticSchema = getSupersonicSemanticSchema(dataSetId);
        List<SemanticModel> semanticModels = Schemas2.toSemanticModels(supersonicSemanticSchema, new OracleColumnTypeToJavaTypeMapper());
        List<SemanticRelationship> semanticRelationships = Schemas2.toSemanticRelationships(supersonicSemanticSchema);
        return Pair.of(semanticModels, semanticRelationships);
    }

    public SemanticSchemaResp getSupersonicSemanticSchema(Long datasetId) {
        SchemaFilterReq schemaFilterReq = new SchemaFilterReq();
        schemaFilterReq.setDataSetId(datasetId);
        return schemaService.fetchSemanticSchema(schemaFilterReq);
    }
    
}
