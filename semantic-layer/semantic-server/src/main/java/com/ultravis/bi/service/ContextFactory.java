package com.ultravis.bi.service;

import com.tencent.supersonic.common.pojo.ModelRela;
import com.tencent.supersonic.headless.api.pojo.SemanticSchema;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.tencent.supersonic.headless.server.service.SchemaService;
import com.ultravis.bi.infra.PromptManager;
import com.ultravis.bi.infra.schema.SchemaRepo;
import com.ultravis.bi.semantic.ParseSqlContext;
import com.ultravis.bi.semantic.rewrite.rewriter.DimensionValueConvertor;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import com.ultravis.bi.text2sql.DimensionValueConvertorFactory;
import com.ultravis.bi.text2sql.GenSqlContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ContextFactory {

    private final SchemaService schemaService;
    private final SchemaRepo schemaRepo;

    public ContextFactory(SchemaService schemaService, SchemaRepo schemaRepo) {
        this.schemaService = schemaService;
        this.schemaRepo = schemaRepo;
    }

    @NotNull
    public ParseSqlContext newParseSqlContext(Set<Long> dataSetIds) {
        SemanticSchema semanticSchema = schemaService.getSemanticSchema(dataSetIds);
        Pair<List<ModelResp>, List<ModelRela>> supersonicModelsAndRelationships =
                schemaRepo.getSupersonicModelsAndRelationships(dataSetIds.iterator().next());
        Pair<List<SemanticModel>, List<SemanticRelationship>> semanticModelsAndRelationships =
                schemaRepo.getSemanticModelsAndRelationships(dataSetIds.iterator().next());
        DimensionValueConvertor dimensionValueConvertor = DimensionValueConvertorFactory.create(semanticSchema);

        return new ParseSqlContext(
                toContextKey(dataSetIds),
                semanticSchema,
                supersonicModelsAndRelationships,
                semanticModelsAndRelationships,
                dimensionValueConvertor
        );
    }

    @NotNull
    private static String toContextKey(Set<Long> dataSetIds) {
        return dataSetIds.stream().map(String::valueOf).sorted().collect(Collectors.joining(","));
    }

    @NotNull
    public GenSqlContext newGenSqlContext(Set<Long> dataSetIds) {
        ParseSqlContext parseSqlContext = newParseSqlContext(dataSetIds);
        List<String> instructions = PromptManager.termsToInstructions(parseSqlContext.semanticSchema);
        return new GenSqlContext(
                parseSqlContext,
                instructions
        );
    }
}
