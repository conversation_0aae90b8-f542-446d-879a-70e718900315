import React, {useEffect, useState} from 'react';
import styles from './style.module.less'
import MarkDown from "../../../components/ChatMsg/MarkDown";
import {CaretDownOutlined, CaretUpOutlined} from '@ant-design/icons';
import {Avatar, Flex} from "antd";
import LogoImg from '/public/logo.png'
import QueueAnim from 'rc-queue-anim';

type Props = {
    loading: boolean;
    reasoning: any
};
const Reasoning: React.FC<Props> = ({
                                        loading,
                                        reasoning,
                                    }) => {

    const [showReasoning, setShowReasoning] = useState(true);

    useEffect(() => {

    }, [reasoning]);


    return (
        <>
            <div className={styles.reasoningMessageBox}>
                <div className={styles.reasoningMessageTip} onClick={() => setShowReasoning(!showReasoning)}>
                    <Flex gap="small" align="center" justify="center">
                        <Avatar size="small" src={<img src={LogoImg} alt="avatar"/>}/>
                        {loading ? "思考中" : "思考完成"}
                        {showReasoning && <CaretDownOutlined/>}
                        {!showReasoning && <CaretUpOutlined/>}
                    </Flex>
                </div>
                <QueueAnim
                    type={['right', 'left']}
                    ease={['easeOutQuart', 'easeInOutQuart']}>
                    {showReasoning ?
                        <div key="a" className={styles.reasoningMessageContent}>
                            <MarkDown  markdown={reasoning} loading={false}/>
                        </div> : null
                    }
                </QueueAnim>
            </div>
        </>
    );
};

export default Reasoning;
