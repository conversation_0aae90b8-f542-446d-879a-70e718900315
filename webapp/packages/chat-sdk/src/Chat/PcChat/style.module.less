.chat {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background-image: url("../../../public/chat_bj.png");
  background-size: cover;

  .chatSection {
    width: 100%;
    height: 100%;
    overflow: hidden;

    .chatApp {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 100%;
      color: rgba(0, 0, 0, 0.87);

      .chatBody {
        display: flex;
        flex: 1;
        height: 100%;

        .chatBodyLeft {
          width: 20%;
          //background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #FFFFFF 100%);

          .topMenuList {
            padding-top: 10%;
            padding-left: 50px;
            color: #832B24;

            .topMenu {
              width: 90%;
              line-height: 3.5rem;
              height: 3.5rem;
              margin-bottom: 1rem;
              background: white;
              border-radius: 10px;
              cursor: pointer;
              text-align: center;
              font-weight: bold;
              border: 1px solid #EED9D7;
            }
          }

          .chatBodyLeftMenuHistoryFgx {
            position: absolute;
            left: 1%;
            bottom: 0;
            width: 18%;
            height: 12.1rem;

            .chatBodyLeftMenuHistoryFgxLine {
              border: 1px solid #6B758B;
            }

            .chatBodyLeftMenuHistoryFgxIcon {
              text-align: center;
              padding-right: 5%;
              margin-top: -0.8rem;
              font-size: 2rem;
              color: #6B758B;
            }
          }

          .chatBodyLeftMenuBox {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 20%;
            height: 12rem;
            display: flex;
            justify-content: center;
            align-items: center;

            .chatBodyLeftMenuList {
              //background: rgba(151, 222, 219, 0.15);
              background-color: rgba(189, 76, 67, 0.20);
            }

            .chatBodyLeftMenuNewChat {
              //background: rgba(194, 171, 248, 0.15);
              background-color: rgba(189, 76, 67, 0.20);
            }

            .chatBodyLeftMenuHistory {
              //background: rgba(169, 181, 249, 0.15);
              background-color: rgba(189, 76, 67, 0.20);
            }

            .chatBodyLeftMenu {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              margin-right: 0.8rem;
              width: 6rem;
              height: 8.4375rem;
              box-shadow: 0 3px 20px 1px rgba(0, 105, 144, 0.1);
              border-radius: 53px 53px 53px 53px;
              cursor: pointer;


              .chatBodyLeftMenuName {
                margin-top: 5px;
                font-weight: 300;
                font-size: 12px;
                color: #832B24;
                text-align: left;
                font-style: normal;
                text-transform: none;
              }
            }
          }

          .chatBodyLeftHeader {
            display: flex;
            align-items: center;
            height: 100px;
            padding-left: 20px;

            .chatBodyLeftHeaderTitle {
              color: #F7F1F1;
              font-size: 24px;
              font-weight: bold;
              padding-left: 10px;
            }
          }

        }

        .chatBodyRight {
          width: 20%;
          //background-color: rgb(252, 252, 252);

          .chatBodyRightUserInfo {
            padding-top: 20px;
            padding-right: 20px;
            text-align: right;
          }
        }

        .chatContent {
          //background: linear-gradient(90deg, rgba(233, 238, 247, 0.65) 0%, rgba(233, 238, 247, 0) 52%, rgba(233, 238, 247, 0.65) 100%);
          background: rgba(189, 76, 67, 0.2);
          position: relative;
          display: flex;
          flex-direction: column;
          width: 60%;
          height: 100%;
          overflow-y: auto;

         .planModeMessageContainer{
           position: relative;
           height: 100%;
           overflow-y: auto;
           scrollbar-width: none; /* 设置滚动条宽度（可选值：auto | thin | none） */
         }

          .chatHeader {
            position: absolute;
            top: 0;
            z-index: 9;
            display: flex;
            align-items: baseline;
            width: 100%;
            padding: 14px 16px;
            background: rgba(243, 243, 247, 0.85);
            backdrop-filter: blur(2px);

            .chatHeaderTitle {
              color: var(--text-color);
              font-weight: 500;
              font-size: 15px;
            }

            .chatHeaderTip {
              max-width: 600px;
              margin-left: 5px;
              overflow: hidden;
              color: var(--text-color-third);
              font-size: 12px;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  &.historyVisible {
    .chatSection {
      .chatApp {
        width: 100%;
        //width: calc(100% - 707px);
      }
    }
  }

  &.mobile {
    .chatSection {
      .chatApp {
        width: 100%;
      }
    }
  }
}

.voiceModel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1111;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  user-select: none; /* Standard syntax */

  .voiceModelImg {
    position: absolute;
    bottom: 20rem;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .voiceModelCancelEntry {
    position: absolute;
    bottom: 12rem;
    left: 2rem;
    right: 0;
    width: 5rem;
    height: 5rem;
    z-index: 1116;
    border-radius: 50%;
    background-color: #bbb;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard syntax */
  }


  .voiceModelCancelEntryAc {
    background-color: white;
    color: #bbbbbb;
    pointer-events: none;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard syntax */
  }

  .voiceModelEntry {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 10rem;
    z-index: 1111;
    border-top-left-radius: 90%;
    border-top-right-radius: 90%;
    background-color: #bbb;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard syntax */
  }

  .voiceModelEntryAc {
    background-color: white;
    color: #bbbbbb;
    pointer-events: none;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard syntax */
  }
}

.showCaseModal {
  :global {
    .ant-modal-content {
      border-radius: 8px;

      .ant-modal-header {
        border-radius: 8px 8px 0 0;
      }

      .ant-modal-body {
        padding: 20px 0 !important;
      }
    }
  }
}

.showCaseDrawer {
  :global {
    .ant-drawer-content {
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;

      .ant-drawer-body {
        padding: 4px 0 !important;
      }
    }
  }
}

:global {
  .ss-chat-recommend-options {
    .ant-table-thead .ant-table-cell {
      padding: 8px !important;
    }

    .ant-table-tbody .ant-table-cell {
      padding: 8px !important;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}
