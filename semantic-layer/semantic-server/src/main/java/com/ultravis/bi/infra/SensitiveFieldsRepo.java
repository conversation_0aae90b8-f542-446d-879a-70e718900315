package com.ultravis.bi.infra;

import com.ultravis.bi.semantic.SensitiveField;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;

@Repository
public class SensitiveFieldsRepo {

    public static List<SensitiveField> listByModels(List<String> modelNames) {
        return List.of(new SensitiveField()
                .setModelName("*")
                .setFieldName("password")
                .setSensitiveLevel(2)
                .setProtectionPolicy(SensitiveField.ProtectionPolicy.MASK));
    }

    public static BiFunction<String, String, Optional<SensitiveField>> createMatcher(List<String> modelsNames) {
        List<SensitiveField> sensitiveFields = SensitiveFieldsRepo.listByModels(modelsNames);
        return (modelName, fieldName) -> sensitiveFields.stream()
                .filter(f -> f.matches(modelName, fieldName))
                .findFirst();
    }
}
