package com.ultravis.bi.semantic.rewrite.rewriter;

import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.dialect.OracleSqlDialect;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.util.SqlBasicVisitor;
import org.apache.calcite.sql.validate.SqlConformanceEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static com.ultravis.bi.semantic.rewrite.rewriter.SemanticSqlParser.withDetailDecoration;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

public class BugFix extends BaseSemanticSqlParserTest {

    BugFix() {
        super(Schemas.kustStudentsSchemas, Schemas.kustStudentRelationships, new SemanticSqlParserConfig()
                .withSqlDialect(OracleSqlDialect.DEFAULT)
                .withSqlConformance(SqlConformanceEnum.LENIENT)
                .withStrictJoin(true)
                .withCompactColumnAlias(true));
    }

    @Test
    void test_analyze_top_level_select_items() {
        semanticSqlParser.transformQuery("""
                SELECT kustXSKSCJXX.KCMC AS 科目名称, AGGREGATE(kustXSKSCJXX_ZCJ_sum) AS 最高成绩
                FROM kustXSKSCJXX
                INNER JOIN DWD_XSJBSJZLB ON kustXSKSCJXX.XH = DWD_XSJBSJZLB.XH WHERE DWD_XSJBSJZLB.XM = '张杰'
                GROUP BY kustXSKSCJXX.KCMC
                ORDER BY 最高成绩 DESC
                LIMIT 1;
                """);

        Assertions.assertEquals(Map.of("科目名称", "kustXSKSCJXX.KCMC"), semanticSqlParser.analysis.topLevelSelectDimensions().getKey());
    }

    @DisplayName("Apache calcite 不支持 FLOOR(interval), 导致后续 unparses 失败")
    @Test
    void test_floor_interval_calcite() {
        SqlParser parser = SqlParser.create(
                """
                        SELECT FLOOR(CURRENT_DATE TO MONTH)
                        FROM kustXSQDXX
                        """);

        assertThrows(ClassCastException.class, () -> {
            SqlNode sqlNode = parser.parseQuery();
            System.out.println(sqlNode.toSqlString(OracleSqlDialect.DEFAULT).getSql());
        });
    }

    @DisplayName("Apache calcite 不支持 FLOOR(interval)")
    @Test
    void test_floor_interval() {
        SqlNode sqlNode = semanticSqlParser.parseQueryNoAnalyze(
                """
                        SELECT FLOOR(CURRENT_DATE TO MONTH)
                        FROM kustXSQDXX
                        """
        );

        Set<String> functionNames = new HashSet<>();
        Set<String> identifiers = new HashSet<>();

        SqlBasicVisitor<Void> visitor = new SqlBasicVisitor<>() {
            @Override
            public Void visit(SqlIdentifier id) {
                identifiers.add(id.toString());
                return super.visit(id);
            }

            @Override
            public Void visit(SqlCall call) {
                String name = call.getOperator().getName();
                functionNames.add(name);
                return super.visit(call);
            }
        };
        sqlNode.accept(visitor);

        Assertions.assertFalse(functionNames.contains("CURRENT_DATE"));
        Assertions.assertTrue(identifiers.contains("CURRENT_DATE"));
    }

    @Test
    void test_floor_interval_ok() {
        String originalSql = """
                SELECT FLOOR(CURRENT_DATE TO MONTH) FROM kustXSQDXX
                """;
        RuntimeException runtimeException = assertThrows(RuntimeException.class, () -> semanticSqlParser.parseQuery(originalSql));
        Assertions.assertEquals("CURRENT_DATE 是一个系统函数, 但是被识别为了标识符. 你可能使用了解析器不支持的语法.", runtimeException.getMessage());
    }

    @Test
    void test_floor_interval_error() {
        String originalSql = """
                SELECT TIMESTAMPADD(YEAR, 2, CURRENT_DATE) FROM kustXSQDXX
                """;
        RuntimeException runtimeException = assertThrows(RuntimeException.class, () -> semanticSqlParser.parseQuery(originalSql));
        Assertions.assertEquals("CURRENT_DATE 是一个系统函数, 但是被识别为了标识符. 你可能使用了解析器不支持的语法.", runtimeException.getMessage());
    }

    @Test
    void test_to_char() {
        assertTransformOk(
                """
                        SELECT TO_CHAR(CURRENT_DATE, 'YYYY-MM-DD') FROM kustXSQDXX
                        """,
                """
                        SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD') FROM (SELECT "XH", "QDSJ", "QDJG"
                        FROM (SELECT "t1"."XH", "t1"."QDSJ", "t1"."QDJG", "t2"."BT", "t2"."NR", "t1"."FZMC", "t2"."XQMC"
                        FROM "LY_DW2"."DWD_XSQDXXZLB" "t1"
                        INNER JOIN "LY_DW2"."DWD_QDNRFBXXZLB" "t2" ON "t1"."QDID" = "t2"."WYBS")) "kustXSQDXX"
                        """
        );
    }

    @Test
    void test_wrap_() {
        String original = """
                SELECT DWD_XSJBSJZLB.XH
                FROM DWD_SSWGWGXXSJB
                INNER JOIN DWD_XSJBSJZLB ON DWD_SSWGWGXXSJB.XH = DWD_XSJBSJZLB.XH
                WHERE DWD_SSWGWGXXSJB.JCSJ >= CURRENT_DATE - INTERVAL '3' MONTH
                AND DWD_SSWGWGXXSJB.JCSJ > DWD_SSWGWGXXSJB.YGSSJ
                GROUP BY DWD_XSJBSJZLB.XH
                HAVING COUNT(*) > 3
                """;
        String expected = """
                SELECT "_main"."XH", "_detail"."XH", "_detail"."XM"
                FROM (SELECT "DWD_XSJBSJZLB"."XH"
                FROM (SELECT "XH", "YGSSJ", "JCSJ"
                FROM (SELECT *
                FROM "LY_DW2"."DWD_SSWGWGXXSJB")) "DWD_SSWGWGXXSJB"
                INNER JOIN (SELECT "XH"
                FROM (SELECT *
                FROM "LY_DW2"."DWD_XSJBSJZLB")) "DWD_XSJBSJZLB" ON "DWD_SSWGWGXXSJB"."XH" = "DWD_XSJBSJZLB"."XH"
                WHERE "DWD_SSWGWGXXSJB"."JCSJ" >= CAST(CURRENT_DATE - INTERVAL '3' MONTH AS TIMESTAMP(0)) AND "DWD_SSWGWGXXSJB"."JCSJ" > "DWD_SSWGWGXXSJB"."YGSSJ"
                GROUP BY "DWD_XSJBSJZLB"."XH"
                HAVING COUNT(*) > CAST(3 AS NUMBER(19))) "_main"
                LEFT JOIN (SELECT "XH", "XM"
                FROM (SELECT "XH", "XM"
                FROM (SELECT *
                FROM "LY_DW2"."DWD_XSJBSJZLB")) "DWD_XSJBSJZLB") "_detail" ON "_detail"."XH" = "_main"."XH"\
                """;

        ParseResult parse = semanticSqlParser.parse(original, withDetailDecoration(true));
        assertEquals(expected, parse.getOutputSql());
    }

    @Test
    void test_type_casting_bigint() {
        String original = """
                SELECT DWD_XSJBSJZLB.XH, DWD_XSJBSJZLB.XM
                FROM DWD_SSWGWGXXSJB
                INNER JOIN DWD_XSJBSJZLB ON DWD_SSWGWGXXSJB.XH = DWD_XSJBSJZLB.XH
                WHERE DWD_SSWGWGXXSJB.JCSJ >= CURRENT_DATE - INTERVAL '3' MONTH
                AND DWD_SSWGWGXXSJB.JCSJ > DWD_SSWGWGXXSJB.YGSSJ
                GROUP BY DWD_XSJBSJZLB.XH, DWD_XSJBSJZLB.XM
                HAVING COUNT(*) > 3
                """;
        String expected = """
                SELECT "DWD_XSJBSJZLB"."XH", "DWD_XSJBSJZLB"."XM"
                FROM (SELECT "XH", "YGSSJ", "JCSJ"
                FROM (SELECT *
                FROM "LY_DW2"."DWD_SSWGWGXXSJB")) "DWD_SSWGWGXXSJB"
                INNER JOIN (SELECT "XH", "XM"
                FROM (SELECT *
                FROM "LY_DW2"."DWD_XSJBSJZLB")) "DWD_XSJBSJZLB" ON "DWD_SSWGWGXXSJB"."XH" = "DWD_XSJBSJZLB"."XH"
                WHERE "DWD_SSWGWGXXSJB"."JCSJ" >= CAST(CURRENT_DATE - INTERVAL '3' MONTH AS TIMESTAMP(0)) AND "DWD_SSWGWGXXSJB"."JCSJ" > "DWD_SSWGWGXXSJB"."YGSSJ"
                GROUP BY "DWD_XSJBSJZLB"."XH", "DWD_XSJBSJZLB"."XM"
                HAVING COUNT(*) > CAST(3 AS NUMBER(19))\
                """;

        ParseResult parse = semanticSqlParser.parse(original);

        assertEquals(expected, parse.getOutputSql());
    }

    @Test
    void test_() {
        String[] args = {"JYSJ", "'yyyy-MM-dd'"};
        String computing = "TO_DATE(%s, %s)".formatted((Object[]) args);
        assertEquals("", computing);
    }

    public static Stream<Arguments> allCases() {
        Stream<Arguments> argumentsStream = Stream.of(
                Arguments.of(
                        """
                                SELECT K.XH, K.KCMC, K.kustXSKSCJXX_ZCJ FROM kustXSKSCJXX K INNER JOIN DWD_XSJBSJZLB S ON K.XH = S.XH WHERE S.XM = '张杰' ORDER BY K.kustXSKSCJXX_ZCJ DESC LIMIT 1;
                                """,
                        """
                                SELECT "K"."XH", "K"."KCMC", "K"."kustXSKSCJXX_ZCJ"
                                FROM (SELECT "XH", "KCMC", "ZCJ" "kustXSKSCJXX_ZCJ"
                                FROM (SELECT "t1"."XH", "t1"."XNXQ", "t1"."SFCXS", "t2"."KCMC", "t2"."XF" "KCXF", "t2"."ZXS" "KCZXS", "t2"."MZXS" "KCMZXS", "t2"."KCFZR", "t3"."JD", "t3"."PSCJ", "t3"."KCCJ", "t3"."ZCJ", "t3"."PJXFJD", "t3"."DJLKSCJ", "t3"."FSLKSCJ", "t3"."KSRQ", "t3"."SFJG"
                                FROM "LY_DW2"."DWD_XKSJLB" "t1"
                                INNER JOIN "LY_DW2"."DWD_KCSJLB" "t2" ON "t1"."KCH" = "t2"."KCH"
                                INNER JOIN "LY_DW2"."DWD_CJZLB" "t3" ON "t1"."XH" = "t3"."XH" AND "t1"."KCH" = "t3"."KCH")) "K"
                                INNER JOIN (SELECT "XH", "XM"
                                FROM (SELECT *
                                FROM "LY_DW2"."DWD_XSJBSJZLB")) "S" ON "K"."XH" = "S"."XH"
                                WHERE "S"."XM" = '张杰'
                                ORDER BY "K"."kustXSKSCJXX_ZCJ" DESC
                                FETCH NEXT 1 ROWS ONLY
                                """
                ),
                Arguments.of(
                        """
                                SELECT COUNT(DISTINCT kustXSQDXX.XH) AS 迟到学生人数
                                FROM kustXSQDXX
                                INNER JOIN DWD_XSJBSJZLB ON kustXSQDXX.XH = DWD_XSJBSJZLB.XH
                                WHERE kustXSQDXX.QDSJ >= DATE '2025-05-01'
                                  AND kustXSQDXX.QDSJ < DATE '2025-06-01'
                                  AND kustXSQDXX.QDJG = '迟到';
                                
                                """,
                        """
                                SELECT COUNT(DISTINCT "kustXSQDXX"."XH") "迟到学生人数"
                                FROM (SELECT "XH", "QDSJ", "QDJG"
                                FROM (SELECT "t1"."XH", "t1"."QDSJ", "t1"."QDJG", "t2"."BT", "t2"."NR", "t1"."FZMC", "t2"."XQMC"
                                FROM "LY_DW2"."DWD_XSQDXXZLB" "t1"
                                INNER JOIN "LY_DW2"."DWD_QDNRFBXXZLB" "t2" ON "t1"."QDID" = "t2"."WYBS")) "kustXSQDXX"
                                INNER JOIN (SELECT "XH"
                                FROM (SELECT *
                                FROM "LY_DW2"."DWD_XSJBSJZLB")) "DWD_XSJBSJZLB" ON "kustXSQDXX"."XH" = "DWD_XSJBSJZLB"."XH"
                                WHERE "kustXSQDXX"."QDSJ" >= CAST(TO_DATE('2025-05-01', 'YYYY-MM-DD') AS TIMESTAMP(0)) AND "kustXSQDXX"."QDSJ" < CAST(TO_DATE('2025-06-01', 'YYYY-MM-DD') AS TIMESTAMP(0)) AND "kustXSQDXX"."QDJG" = '迟到'         
                                """
                ),
                Arguments.of(
                        """
                                SELECT DWD_XSJBSJZLB.* FROM DWD_XSJBSJZLB WHERE DWD_XSJBSJZLB.XM = '张杰';
                                """,
                        """
                                SELECT "DWD_XSJBSJZLB".*
                                FROM (SELECT "XH", "XM", "HYZKM", "XBM", "SFDSZN", "GJDQM", "JGM", "XYDM", "BJMC", "TC", "AH", "YHKH", "CSRQ", "SFZJH", "SFZJLXM", "GATQWM", "JKZKM", "ZZMMM", "XXM", "MZM", "XYZJM", "NJ", "RXNJ", "ZYMC", "XSLBM", "YXMC", "XJZT", "SFZX", "SFZJ", "SFYXJ"
                                FROM (SELECT *
                                FROM "LY_DW2"."DWD_XSJBSJZLB")) "DWD_XSJBSJZLB"
                                WHERE "DWD_XSJBSJZLB"."XM" = '张杰'
                                """
                ),
                Arguments.of(
                        """
                                SELECT COUNT(DISTINCT kustXSQDXX.XH) AS 迟到学生人数
                                FROM kustXSQDXX
                                INNER JOIN DWD_XSJBSJZLB ON kustXSQDXX.XH = DWD_XSJBSJZLB.XH
                                WHERE kustXSQDXX.QDSJ >= FLOOR(TIMESTAMPADD(MONTH, -1, CURRENT_DATE) TO MONTH)
                                  AND kustXSQDXX.QDSJ <  FLOOR(CURRENT_DATE TO MONTH)
                                  AND kustXSQDXX.QDJG = '迟到';
                                """,
                        ""
                ),
                // FIXME org/apache/calcite/sql/dialect/OracleSqlDialect.java:204
                // FIXME MONTH 无法被转为 SQLLiteral
                // TODO Oracle 对 FLOOR(dateTime TO timeUnit) 的解析有BUG
                Arguments.of(
                        """
                                SELECT COUNT(DISTINCT kustXSQDXX.XH) AS 迟到学生人数
                                FROM kustXSQDXX
                                INNER JOIN DWD_XSJBSJZLB ON kustXSQDXX.XH = DWD_XSJBSJZLB.XH
                                WHERE FLOOR(kustXSQDXX.QDSJ TO "MONTH") = FLOOR(TIMESTAMPADD(MONTH, -1, CURRENT_DATE) TO MONTH)
                                AND kustXSQDXX.QDJG = '迟到';
                                """,
                        "<PARSE ERROR>"
                ),
                // TODO INTERVAL 不被支持(可选: TIMESTAMPADD(MONTH, -1, CURRENT_DATE))
                // TODO DATE_TRUNC 不被支持
                Arguments.of(
                        """
                                SELECT COUNT(DISTINCT kustXSQDXX.XH) AS 迟到学生人数
                                FROM kustXSQDXX
                                INNER JOIN DWD_XSJBSJZLB ON kustXSQDXX.XH = DWD_XSJBSJZLB.XH
                                WHERE TIMESTAMPDIFF(MONTH, kustXSQDXX.QDSJ, CURRENT_DATE) = -1
                                AND kustXSQDXX.QDJG = '迟到';
                                """,
                        "<PARSE FAILED>"
                ),
                Arguments.of(
                        """
                                SELECT DWD_XSJBSJZLB.XH, DWD_XSJBSJZLB.XM
                                FROM DWD_XSJBSJZLB
                                INNER JOIN DWD_SSWGWGXXSJB ON DWD_XSJBSJZLB.XH = DWD_SSWGWGXXSJB.XH
                                WHERE DWD_SSWGWGXXSJB.JCSJ > DWD_SSWGWGXXSJB.YGSSJ
                                  AND DWD_SSWGWGXXSJB.JCSJ >= CURRENT_DATE - Interval '90' DAY
                                GROUP BY DWD_XSJBSJZLB.XH, DWD_XSJBSJZLB.XM
                                HAVING COUNT(*) > 3
                                """,
                        """
                                SELECT "DWD_XSJBSJZLB"."XH", "DWD_XSJBSJZLB"."XM"
                                FROM (SELECT "XH", "XM"
                                FROM (SELECT *
                                FROM "LY_DW2"."DWD_XSJBSJZLB")) "DWD_XSJBSJZLB"
                                INNER JOIN (SELECT "XH", "YGSSJ", "JCSJ"
                                FROM (SELECT *
                                FROM "LY_DW2"."DWD_SSWGWGXXSJB")) "DWD_SSWGWGXXSJB" ON "DWD_XSJBSJZLB"."XH" = "DWD_SSWGWGXXSJB"."XH"
                                WHERE "DWD_SSWGWGXXSJB"."JCSJ" > "DWD_SSWGWGXXSJB"."YGSSJ" AND "DWD_SSWGWGXXSJB"."JCSJ" >= CAST(CURRENT_DATE - INTERVAL '90' DAY AS TIMESTAMP(0))
                                GROUP BY "DWD_XSJBSJZLB"."XH", "DWD_XSJBSJZLB"."XM"
                                HAVING COUNT(*) > CAST(3 AS BIGINT)
                                """
                ),
                Arguments.of(
                        // COUNT 被用作别名，导致解析器报错
                        """
                                SELECT Q.QJLX, COUNT(*) AS COUNT
                                FROM DWD_QJXXZLB Q
                                INNER JOIN DWD_XSJBSJZLB S ON Q.XH = S.XH
                                WHERE EXTRACT(YEAR FROM Q.QJSJ) = EXTRACT(YEAR FROM CURRENT_DATE) GROUP BY Q.QJLX
                                """,
                        """
                                SELECT Q.QJLX, COUNT(*) AS "COUNT"
                                FROM DWD_QJXXZLB Q
                                INNER JOIN DWD_XSJBSJZLB S ON Q.XH = S.XH
                                WHERE EXTRACT(YEAR FROM Q.QJSJ) = EXTRACT(YEAR FROM CURRENT_DATE) GROUP BY Q.QJLX
                                """
                ),
                Arguments.of(
                        //
                        """
                                SELECT DWD_QJXXZLB.QJLX AS 请假类型, COUNT(*) AS 请假次数
                                FROM DWD_QJXXZLB
                                INNER JOIN DWD_XSJBSJZLB ON DWD_QJXXZLB.XH = DWD_XSJBSJZLB.XH
                                WHERE EXTRACT(YEAR FROM DWD_QJXXZLB.QJSJ) = EXTRACT(YEAR FROM CURRENT_DATE)
                                GROUP BY DWD_QJXXZLB.QJLX;
                                """,
                        """
                                """
                ),
                Arguments.of(
                        """
                                SELECT DWD_QJXXZLB.QJLX AS "请假类型", COUNT(*) AS "数量"
                                FROM DWD_QJXXZLB
                                WHERE EXTRACT(YEAR FROM DWD_QJXXZLB.KSSJ) = EXTRACT(YEAR FROM CURRENT_DATE)
                                GROUP BY DWD_QJXXZLB.QJLX;
                                """,
                        """
                                SELECT "DWD_QJXXZLB"."QJLX" "请假类型", COUNT(*) "数量"
                                FROM (SELECT "QJLX", "TO_DATE"("KSSJ", 'yyyy-MM-dd') "KSSJ"
                                FROM (SELECT *
                                FROM "LY_DW2"."DWD_QJXXZLB")) "DWD_QJXXZLB"
                                WHERE EXTRACT(YEAR FROM "DWD_QJXXZLB"."KSSJ") = EXTRACT(YEAR FROM CURRENT_DATE)
                                GROUP BY "DWD_QJXXZLB"."QJLX"
                                """
                ),
                Arguments.of(
                        """
                                SELECT DWD_QJXXZLB.XH, DWD_QJXXZLB.KSSJ, DWD_QJXXZLB.JSSJ, TIMESTAMPDIFF(DAY, DWD_QJXXZLB.KSSJ, DWD_QJXXZLB.JSSJ) AS leave_duration_days
                                FROM DWD_QJXXZLB
                                WHERE TIMESTAMPDIFF(DAY, DWD_QJXXZLB.KSSJ, DWD_QJXXZLB.JSSJ) > 7;
                                """,
                        """
                                """
                )
        );
        return argumentsStream;
    }

    @ParameterizedTest
    @MethodSource("allCases")
    void test_all_cases(String originalSql, String expectedSql) {
        assertTransformOk(originalSql, expectedSql);
    }
}
