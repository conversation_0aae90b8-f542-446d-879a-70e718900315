import React, {useEffect, useState} from 'react';
import {Button, Drawer} from 'antd';
import NextChatMobileTableItem from "../NextChatMobileTableItem";
import styles from './style.module.less'
import {CloseOutlined} from "@ant-design/icons";

type Props = {
    open: boolean;
    tableColumns: any;
    tableDataSource: any;
    onCloseMoreModel: () => void;
};

const NextChatMobileTableDrawer: React.FC<Props> = ({
                                                        open = false,
                                                        tableColumns,
                                                        tableDataSource,
                                                        onCloseMoreModel
                                                    }) => {
    const [dataSource, setDataSource] = useState<any>([]);

    useEffect(() => {
        setDataSource(tableDataSource || [])
    }, [tableDataSource]);

    return (
        <>
            <Drawer
                title="更多内容"
                placement="bottom"
                closable={false}
                onClose={onCloseMoreModel}
                open={open}
                key="bottom"
                height="90vh"
                style={{borderRadius:'20px 20px 0px 0px'}}
                extra={
                    <Button type="text" onClick={onCloseMoreModel} icon={<CloseOutlined />}/>
                }
            >
                <div className={styles.nextChatMobileTableDrawerBox}>
                    {dataSource.map((record: any, index: number) => {
                        return (
                            <NextChatMobileTableItem
                                key={index}
                                record={record}
                                tableColumns={tableColumns}
                                tableDataSourceLength={tableDataSource.length}
                                />
                        )
                    })}
                </div>
            </Drawer>
        </>
    );
};

export default NextChatMobileTableDrawer;
