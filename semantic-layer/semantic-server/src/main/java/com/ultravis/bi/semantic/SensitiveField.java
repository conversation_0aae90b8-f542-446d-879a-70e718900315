package com.ultravis.bi.semantic;

import lombok.Data;
import lombok.experimental.Accessors;

import static java.util.Collections.nCopies;

@Accessors(chain = true)
@Data
public class SensitiveField {
    String modelName;
    String fieldName;
    int sensitiveLevel;
    ProtectionPolicy protectionPolicy;

    public enum ProtectionPolicy {
        MASK() {
            @Override
            public String doFilter(Object value) {
                return "******";
            }
        },
        PARTIAL() {
            @Override
            public String doFilter(Object value) {
                String str = String.valueOf(value);
                if (str.length() <= 4 * 2) {
                    return String.join("", nCopies(str.length(), "*"));
                }
                return str.substring(0, 2) + "****" + str.substring(str.length() - 2);
            }
        };

        public String apply(Object value) {
            if (value == null) {
                return null;
            }
            return doFilter(value);
        }

        protected abstract String doFilter(Object value);
    }

    public boolean isGlobal() {
        return "*".equals(modelName);
    }

    public boolean matches(String modelName, String fieldName) {
        return this.fieldName.equals(fieldName) && (isGlobal() || this.modelName.equals(modelName));
    }
}
