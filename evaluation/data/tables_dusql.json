[{"db_id": "internet", "table_names": ["company", "brand", "company_brand_revenue", "company_revenue"], "column_names": [[-1, "*"], [0, "company_id"], [0, "company_name"], [0, "headquarter_address"], [0, "company_established_time"], [0, "founder"], [0, "ceo"], [0, "annual_turnover"], [0, "employee_count"], [1, "brand_id"], [1, "brand_name"], [1, "brand_established_time"], [1, "company_id"], [1, "legal_representative"], [1, "registered_capital"], [2, "year_time"], [2, "brand_id"], [2, "revenue"], [2, "profit"], [2, "revenue_growth_year_on_year"], [2, "profit_growth_year_on_year"], [3, "company_id"], [3, "brand_id"], [3, "revenue_proportion"], [3, "profit_proportion"], [3, "expenditure_proportion"]], "table_names_original": ["company", "brand", "company_brand_revenue", "company_revenue"], "column_names_original": [[-1, "*"], [0, "company_id"], [0, "company_name"], [0, "headquarter_address"], [0, "company_established_time"], [0, "founder"], [0, "ceo"], [0, "annual_turnover"], [0, "employee_count"], [1, "brand_id"], [1, "brand_name"], [1, "brand_established_time"], [1, "company_id"], [1, "legal_representative"], [1, "registered_capital"], [2, "year_time"], [2, "brand_id"], [2, "revenue"], [2, "profit"], [2, "revenue_growth_year_on_year"], [2, "profit_growth_year_on_year"], [3, "company_id"], [3, "brand_id"], [3, "revenue_proportion"], [3, "profit_proportion"], [3, "expenditure_proportion"]], "column_types": ["text", "number", "text", "text", "time", "text", "time", "number", "number", "number", "text", "time", "text", "text", "number", "time", "number", "number", "number", "number", "number", "number", "number", "number", "number", "number"], "foreign_keys": [[12, 1], [21, 1], [22, 9], [16, 9]], "primary_keys": [1, 9]}, {"db_id": "china_travel_agency", "table_names": ["travel_agency", "outbound_travel_routes", "country_outbound_travel", "domestic_travel_routes", "cruise_route"], "column_names": [[-1, "*"], [0, "travel_agency_id"], [0, "travel_agency_name"], [0, "travel_agency_level"], [0, "number_countrie_outbound_travel"], [0, "number_domestic_tourist_cities"], [0, "number_outbound_travel_routes"], [0, "number_domestic_travel_routes"], [0, "asia_ranking"], [0, "number_overseas_tourists_received"], [0, "number_overseas_companies"], [0, "number_holding_subsidiaries"], [0, "number_traveling_salesmen_business_relationships"], [0, "number_duty_free_shops"], [1, "outbound_route_id"], [1, "outbound_route_name"], [1, "travel_agency_id"], [1, "outbound_departure_city"], [1, "outbound_days"], [1, "adult_price"], [1, "child_price"], [1, "countries"], [1, "attractions"], [1, "total_ticket_price"], [2, "outbound_travel_route_id"], [2, "nation"], [2, "travel_days"], [2, "outbound_number_attractions"], [3, "domestic_travel_route_id"], [3, "domestic_travel_route_name"], [3, "travel_agency_id"], [3, "domestic_departure_city"], [3, "domestic_days"], [3, "presale_price"], [3, "tour_price"], [3, "number_people_group"], [3, "personal_price"], [3, "domestic_number_attractions"], [4, "cruise_route_id"], [4, "cruise_route_name"], [4, "travel_agency_id"], [4, "cruise_departure_city"], [4, "cruise_days"], [4, "interior_cabin_price"], [4, "sea_view_room_price"], [4, "balcony_room_price"], [4, "sailing_area"], [4, "cruise_line"]], "table_names_original": ["travel_agency", "outbound_travel_routes", "country_outbound_travel", "domestic_travel_routes", "cruise_route"], "column_names_original": [[-1, "*"], [0, "travel_agency_id"], [0, "travel_agency_name"], [0, "travel_agency_level"], [0, "number_countrie_outbound_travel"], [0, "number_domestic_tourist_cities"], [0, "number_outbound_travel_routes"], [0, "number_domestic_travel_routes"], [0, "asia_ranking"], [0, "number_overseas_tourists_received"], [0, "number_overseas_companies"], [0, "number_holding_subsidiaries"], [0, "number_traveling_salesmen_business_relationships"], [0, "number_duty_free_shops"], [1, "outbound_route_id"], [1, "outbound_route_name"], [1, "travel_agency_id"], [1, "outbound_departure_city"], [1, "outbound_days"], [1, "adult_price"], [1, "child_price"], [1, "countries"], [1, "attractions"], [1, "total_ticket_price"], [2, "outbound_travel_route_id"], [2, "nation"], [2, "travel_days"], [2, "outbound_number_attractions"], [3, "domestic_travel_route_id"], [3, "domestic_travel_route_name"], [3, "travel_agency_id"], [3, "domestic_departure_city"], [3, "domestic_days"], [3, "presale_price"], [3, "tour_price"], [3, "number_people_group"], [3, "personal_price"], [3, "domestic_number_attractions"], [4, "cruise_route_id"], [4, "cruise_route_name"], [4, "travel_agency_id"], [4, "cruise_departure_city"], [4, "cruise_days"], [4, "interior_cabin_price"], [4, "sea_view_room_price"], [4, "balcony_room_price"], [4, "sailing_area"], [4, "cruise_line"]], "column_types": ["text", "number", "text", "text", "number", "number", "number", "number", "number", "number", "number", "number", "number", "number", "number", "text", "number", "text", "number", "number", "number", "number", "number", "number", "number", "text", "number", "number", "number", "text", "number", "text", "number", "number", "number", "number", "number", "number", "number", "text", "number", "text", "number", "number", "number", "number", "text", "text"], "foreign_keys": [[40, 1], [24, 14], [30, 1], [16, 1]], "primary_keys": [1, 14, 28, 38]}]