import React, {useEffect, useState} from 'react';
import styles from './style.module.less'
import ExecuteItem from "../../../components/ChatItem/ExecuteItem";
import {ChatContextType, FilterItemType} from "../../../common/type";
import {SEARCH_EXCEPTION_TIP} from "../../../common/constants";

type Props = {
    executeData: any
};
const PlanModeExecute: React.FC<Props> = ({
                                              executeData,
                                          }) => {
    const [chatContext, setChatContext] = useState<ChatContextType>();
    const [executeTip, setExecuteTip] = useState('');

    useEffect(() => {
        init()
    }, [executeData]);

    const init = () => {
        if (executeData) {
            const _chatContext = executeData.chatContext
            setChatContext(_chatContext)
            const queryState = executeData.queryState
            const response = executeData.response
            const queryColumns = executeData.queryColumns
            const queryResults = executeData.queryResults
            const queryMode = executeData.queryMode
            let tip = "";
            if (queryState !== 'SUCCESS') {
                tip = response && typeof response === 'string' ? response : SEARCH_EXCEPTION_TIP;
            } else if (
                (queryColumns && queryColumns.length > 0 && queryResults) ||
                queryMode === 'WEB_PAGE' ||
                queryMode === 'WEB_SERVICE' ||
                queryMode === 'PLAIN_TEXT'
            ) {
                tip = '';
            }
            if (_chatContext) {
                setExecuteTip('');
                return true;
            }
            setExecuteTip(tip || SEARCH_EXCEPTION_TIP);
        }
    }

    return (
        <div className={styles.planModeExecuteBox}>
            <ExecuteItem
                question={''}
                executeLoading={false}
                currentParseInfo={chatContext}
                chartIndex={0}
                data={executeData}
                executeTip={executeTip}
                dateInfo={{
                    dateList: [],
                    dateMode: '',
                    period: '',
                    startDate: '',
                    endDate: '',
                    text: '',
                    unit: 0
                }}
                dimensionFilters={chatContext?.dimensionFilters || []}
                onRefresh={function (): void {
                    throw new Error('Function not implemented.');
                }}
                onFiltersChange={function (filters: FilterItemType[]): void {
                    throw new Error('Function not implemented.');
                }}
                onDateInfoChange={function (dateRange: any): void {
                    throw new Error('Function not implemented.');
                }}
                onSwitchEntity={function (entityId: string): void {
                    throw new Error('Function not implemented.');
                }}
                handlePresetClick={undefined}
            />
        </div>
    );
};

export default PlanModeExecute;
