package com.ultravis.bi.semantic;

import com.ultravis.bi.infra.ViewInfoRepo;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticColumn;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import com.ultravis.bi.utils.SqlTemplateCompiler;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toMap;

public class RelationshipViewManager {

    private static final Map<ParseSqlContext, RelationshipViewManager> MANAGERS = new ConcurrentHashMap<>();

    private final SemanticLangParser semanticLangParser;
    private final ParseSqlContext ctx;
    private final List<SemanticModel> semanticModels;
    private final List<SemanticRelationship> semanticRelationships;
    private final Map<String, ParsedRelationshipView> PARSED_VIEWS = new ConcurrentHashMap<>();

    public RelationshipViewManager(SemanticLangParser semanticLangParser, ParseSqlContext ctx) {
        this.semanticLangParser = semanticLangParser;
        this.ctx = ctx;
        this.semanticModels = ctx.semanticModelsAndRelationships.getLeft();
        this.semanticRelationships = ctx.semanticModelsAndRelationships.getRight();
    }

    public void parse() {
        semanticRelationships.forEach(rel -> {
            Optional<ParsedRelationshipView> parse = parse(rel, rel.fromModelName(), rel.toModelName());
            parse.ifPresent(o -> {
                PARSED_VIEWS.put(o.name, o);
            });
            Optional<ParsedRelationshipView> parse1 = parse(rel, rel.toModelName(), rel.fromModelName());
            parse1.ifPresent(o -> {
                PARSED_VIEWS.put(o.name, o);
            });
        });
    }

    public static Optional<ParsedRelationshipView> getView(String viewKey, ParseSqlContext ctx) {
        RelationshipViewManager manager = MANAGERS.computeIfAbsent(ctx, k -> {
            var m = new RelationshipViewManager(new SemanticLangParser(), ctx);
            m.parse();
            return m;
        });
        return manager.getView(viewKey);
    }

    public static Optional<ParsedRelationshipView> getView(String from, String to, ParseSqlContext ctx) {
        return getView(from + " -> " + to, ctx);
    }

    public Optional<ParsedRelationshipView> getView(String viewKey) {
        return Optional.ofNullable(PARSED_VIEWS.get(viewKey));
    }

    public Optional<ParsedRelationshipView> getView(String from, String to) {
        return getView(from + " -> " + to);
    }

    public record ParsedRelationshipView(
            String name,
            String description,
            String sqlTemplate,
            ParseResult parseResult,
            Map<String, String> paramTypeDesc) {

    }

    @NotNull
    private Optional<ParsedRelationshipView> parse(SemanticRelationship rel, String driving, String driven) {
        Optional<ViewInfoRepo.ViewInfo> viewInfoOpt = ViewInfoRepo.getViewInfo(driven);
        if (viewInfoOpt.isEmpty()) {
            return Optional.empty();
        }

        ViewInfoRepo.ViewInfo viewInfo = viewInfoOpt.get();
        SemanticModel drivingModel = findSemanticModel(driving);
        var filter = makeFilterForPrimaryKeys(drivingModel);

        String sqlTemplate = """
                SELECT %s.*
                FROM %s
                INNER JOIN (%s) AS %s ON %s
                WHERE %s
                """
                .formatted(
                        driven,
                        driving,
                        StringUtils.stripEnd(viewInfo.sql(), " ;\n"),
                        driven,
                        rel.joinCondition(),
                        filter.condition()
                );

        String compiledSql = SqlTemplateCompiler.compile(sqlTemplate, filter.paramExampleValues(), filter.paramTypeDesc());
        ParseResult parseResult = semanticLangParser.parseSql(compiledSql, ctx);

        return Optional.of(new ParsedRelationshipView(
                "%s -> %s".formatted(driving, driven),
                viewInfo.description(),
                sqlTemplate,
                parseResult,
                filter.paramTypeDesc()
        ));
    }

    private static Object getExampleValueForType(String typeName) {
        return switch (typeName) {
            case "string" -> "example string";
            case "number", "integer", "double", "float" -> 1;
            case "boolean" -> true;
            case "date" -> "2024-12-25";
            default -> "example";
        };
    }

    record FilterInfo(String condition, Map<String, String> paramTypeDesc, Map<String, Object> paramExampleValues) {
    }

    @NotNull
    private static FilterInfo makeFilterForPrimaryKeys(SemanticModel fromModel) {
        List<SemanticColumn> primaryKeys = getPrimaryKeys(fromModel).toList();

        String condition = primaryKeys.stream()
                .map(RelationshipViewManager::makeParameterizedCondition)
                .collect(Collectors.joining(" AND "));

        Map<String, String> typeDesc = primaryKeys.stream()
                .collect(toMap(SemanticColumn::getName, col -> col.type.getSimpleName().toLowerCase()));

        Map<String, Object> exampleValues = getExampleValues(typeDesc);

        return new FilterInfo(condition, typeDesc, exampleValues);
    }

    private static Map<String, Object> getExampleValues(Map<String, String> paramTypes) {
        return paramTypes.entrySet().stream()
                .collect(toMap(Map.Entry::getKey, ent -> getExampleValueForType(ent.getValue())));
    }

    @NotNull
    private static Stream<SemanticColumn> getPrimaryKeys(SemanticModel fromModel) {
        return fromModel.getColumns().stream()
                .filter(SemanticColumn::isPrimaryKey);
    }

    private SemanticModel findSemanticModel(String fromModelName) {
        return semanticModels.stream()
                .filter(m -> m.name.equals(fromModelName))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("模型未找到: " + fromModelName));
    }

    private static String makeParameterizedCondition(SemanticColumn col) {
        return col.qualifiedColumnName() + " = " + ":" + col.getName();
    }
}
