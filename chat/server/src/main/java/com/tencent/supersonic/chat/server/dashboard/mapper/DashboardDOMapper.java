package com.tencent.supersonic.chat.server.dashboard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.supersonic.chat.api.pojo.response.DashboardResp;
import com.tencent.supersonic.chat.server.dashboard.dataobject.DashboardDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DashboardDOMapper extends BaseMapper<DashboardDO> {

    /**
     * 获取自己所有的看板
     *
     * @param userId 用户ID
     * @return 看板
     */
    List<DashboardResp> getAll(@Param("userId") Long userId, @Param("title") String title, @Param("status") Integer status);

}
