package com.tencent.supersonic.chat.server.service.impl;

import com.tencent.supersonic.chat.api.pojo.request.DashboardFilter;
import com.tencent.supersonic.chat.api.pojo.response.DashboardResp;
import com.tencent.supersonic.chat.server.dashboard.dataobject.DashboardDO;
import com.tencent.supersonic.chat.server.dashboard.repository.DashboardRepository;
import com.tencent.supersonic.chat.server.service.DashboardService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30
 **/
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final DashboardRepository dashboardDORepository;

    @Override
    public Long createDashboard(DashboardDO request) {
        request.setCreatedAt(new Date());
        String queryText = request.getTitle();
        request.setTitle(StringUtils.isNotEmpty(queryText) ? queryText : "自定义看板");
        return dashboardDORepository.createDashboard(request);
    }

    @Override
    public Long updateDashboard(DashboardDO request) {
        request.setUpdatedAt(new Date());
        return dashboardDORepository.updateDashboard(request);
    }

    @Override
    public void deleteDashboard(String id) {
        dashboardDORepository.deleteDashboard(id);
    }

    @Override
    public List<DashboardResp> getAllDashboard(DashboardFilter filter) {
        return dashboardDORepository.getAllDashboard(filter);
    }
}
