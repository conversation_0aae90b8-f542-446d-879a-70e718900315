package com.ultravis.bi.semantic;

import com.tencent.supersonic.common.pojo.enums.EngineType;
import com.tencent.supersonic.headless.api.pojo.response.DatabaseResp;
import com.tencent.supersonic.headless.core.pojo.Database;
import com.tencent.supersonic.headless.core.utils.SqlUtils;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.rmi.ServerException;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class DataLoader {

    private final SqlUtils sqlUtils;

    public DataLoader(SqlUtils sqlUtils) {
        this.sqlUtils = sqlUtils;
    }

    @NotNull
    public DataLoadResult loadDataByParsedSql(
            ParseResult parseResult,
            DatabaseResp database,
            ParseSqlContext parseSqlContext) {
        List<Map<String, Object>> data = execSql(parseResult.getOutputSql(), database);
        DataLoadResult execSqlResult = new DataLoadResult()
                .setParseResult(parseResult)
                .setData(data);

        // 数据集后处理
        PostDataLoadProcessor.addColumnMetaInfo(parseSqlContext.supersonicModelsAndRelationships.getKey(), execSqlResult);
        PostDataLoadProcessor.addDescriptionColumns(parseSqlContext.semanticSchema, parseSqlContext.supersonicModelsAndRelationships.getKey(), execSqlResult);
        // TODO SemanticLangParser 可以放入 parse context 中
        PostDataLoadProcessor.addRelatedViewQueryGuidance(parseSqlContext, execSqlResult);
        PostDataLoadProcessor.maskSensitiveFields();

        return execSqlResult;
    }

    private List<Map<String, Object>> execSql(String sql, DatabaseResp databaseResp) {
        Database database = Database.builder().name(databaseResp.getName())
                .type(EngineType.fromString(databaseResp.getType()))
                .url(databaseResp.getUrl())
                .username(databaseResp.getUsername()).password(databaseResp.getPassword())
                .build();
        try {
            log.info("开始执行 SQL: {}", sql);
            List<Map<String, Object>> result = sqlUtils.init(database).execute(sql);
            log.info("执行 SQL 成功.");
            return result;
        } catch (ServerException e) {
            log.error("执行 SQL 失败.", e);
            throw new RuntimeException(e);
        }
    }
}
