import styles from './style.module.less';
import {AgentType} from '../../type';
import {isMobile} from '../../../utils/utils';
import LogoImg from '/public/agen_logo.png'
import {Avatar} from "antd";

type Props = {
    currentAgent?: AgentType;
    onSendMsg: (value: string) => void;
};

const AgentTip: React.FC<Props> = ({currentAgent, onSendMsg}) => {
    if (!currentAgent) {
        return null;
    }
    return (
        <div className={styles.agentTip}>
            <div className={styles.agentTipMsg}
                 style={{width: isMobile ? '18rem' : '39rem', height: isMobile ? '12rem' : '26rem'}}>
                {!isMobile && <div>
                    <Avatar className={styles.avatar} size={100} src={<img src={LogoImg} alt="avatar"/>}/>
                </div>}
                <div className={styles.title}>
                    您好，智能助理【{currentAgent.name}
                    】将与您对话，试着问：
                </div>
                <div className={styles.content}>
                    <div className={styles.examples}>
                        {currentAgent.examples?.length > 0 ? (
                            currentAgent.examples.map(example => (
                                <div
                                    key={example}
                                    className={styles.example}
                                    onClick={() => {
                                        onSendMsg(example);
                                    }}
                                >
                                    “{example}”
                                </div>
                            ))
                        ) : (
                            <div className={styles.example}>{currentAgent.description}</div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AgentTip;
