package com.ultravis.bi.service;

import com.tencent.supersonic.headless.api.pojo.response.DatabaseResp;
import com.tencent.supersonic.headless.server.service.DatabaseService;
import com.ultravis.bi.semantic.*;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import com.ultravis.bi.utils.SqlTemplateCompiler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
public class DataLoaderService {

    public static final long DATA_SET_ID = 18L;
    public static final long DATABASE_ID = 2L;

    private final ContextFactory contextFactory;
    private final SemanticLangParser semanticLangParser;
    private final DatabaseService databaseService;
    private final DataLoader dataLoader;

    public DataLoaderService(ContextFactory contextFactory, SemanticLangParser semanticLangParser, DatabaseService databaseService, DataLoader dataLoader) {
        this.contextFactory = contextFactory;
        this.semanticLangParser = semanticLangParser;
        this.databaseService = databaseService;
        this.dataLoader = dataLoader;
    }

    public DataLoadResult loadDataBySemanticSql(String sql) {
        ParseSqlContext parseSqlContext = contextFactory.newParseSqlContext(Set.of(DATA_SET_ID));
        ParseResult parseResult = semanticLangParser.parseSql(sql, parseSqlContext);

        DatabaseResp database = databaseService.getDatabase(DATABASE_ID);
        return dataLoader.loadDataByParsedSql(parseResult, database, parseSqlContext);
    }

    public DataLoadResult loadDataByView(String viewName, Map<String, Object> params) {
        ParseSqlContext parseSqlContext = contextFactory.newParseSqlContext(Set.of(DATA_SET_ID));

        Optional<RelationshipViewManager.ParsedRelationshipView> view = RelationshipViewManager.getView(viewName, parseSqlContext);
        if (view.isEmpty()) {
            throw new RuntimeException("视图未找到: " + viewName);
        }

        String sql = SqlTemplateCompiler.compile(view.get().sqlTemplate(), params, view.get().paramTypeDesc());
        ParseResult parseResult = semanticLangParser.parseSql(sql, parseSqlContext);

        DatabaseResp database = databaseService.getDatabase(DATABASE_ID);
        return dataLoader.loadDataByParsedSql(parseResult, database, parseSqlContext);
    }
}
