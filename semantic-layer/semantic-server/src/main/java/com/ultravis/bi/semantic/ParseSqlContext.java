package com.ultravis.bi.semantic;

import com.tencent.supersonic.common.pojo.ModelRela;
import com.tencent.supersonic.headless.api.pojo.SemanticSchema;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.ultravis.bi.semantic.rewrite.rewriter.DimensionValueConvertor;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

@AllArgsConstructor
public class ParseSqlContext {
    public String contextKey;
    public SemanticSchema semanticSchema;
    public Pair<List<ModelResp>, List<ModelRela>> supersonicModelsAndRelationships;
    public Pair<List<SemanticModel>, List<SemanticRelationship>> semanticModelsAndRelationships;
    public DimensionValueConvertor dimensionValueConvertor;

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;

        ParseSqlContext that = (ParseSqlContext) o;
        return contextKey.equals(that.contextKey);
    }

    @Override
    public int hashCode() {
        return contextKey.hashCode();
    }
}
