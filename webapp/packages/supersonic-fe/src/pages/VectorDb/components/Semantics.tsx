import React, { ReactNode, useEffect, useState } from 'react';
import styles from './style.less';
import {
  batchDeleteVectorDbCollectionField,
  getVectorDbCollections,
  querySemantics,
} from '@/pages/VectorDb/services';
import Search from 'antd/es/input/Search';
import { SearchProps } from 'antd/lib/input';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Flex, message, Popconfirm, Space, Table } from 'antd';
import { AntDesignOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { uuid } from '@/utils/utils';
import ReactJson from 'react-json-view';

type VectorDbCollection = { id: string; name: string; checked: boolean };
type VectorDbCollectionField = { id: string; score?: number; entity: string; text: string };

const SemanticsPage: React.FC = () => {
  const [vectorDbCollectionList, setVectorDbCollectionList] = useState<VectorDbCollection[]>([]);
  const [vectorDbCollectionFiledList, setVectorDbCollectionFiledList] = useState<
    VectorDbCollectionField[]
  >([]);
  const [currentSearchText, setCurrentSearchText] = useState('');
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(0);

  useEffect(() => {
    getVectorDbCollections().then((resp) => {
      const { code, data } = resp;
      if (code === 200) {
        const list = data?.collectionNames || [];
        setVectorDbCollectionList(
          list.map((item: string, index: number) => {
            return { id: uuid(), name: item, checked: index === 0 };
          }),
        );
      }
    });
  }, []);
  const handlerDelete = (ids: string[]) => {
    const collectionName = vectorDbCollectionList.find((item) => item.checked)?.name || '';
    batchDeleteVectorDbCollectionField({ ids, collectionName }).then((resp) => {
      const { code } = resp;
      if (200 === code) {
        message.success('删除成功');
        queryDbFiled();
      } else {
        message.error('删除失败');
      }
    });
  };

  function isJsonString(str: any) {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  }

  const columns: ProColumns<VectorDbCollectionField>[] = [
    {
      title: '文本',
      dataIndex: 'text',
      render: (_) => <a>{_}</a>,
    },
    {
      title: '分值',
      dataIndex: 'score',
    },
    {
      title: 'metadata',
      dataIndex: 'entity',
      width: 500,
      render: (text: ReactNode, record: VectorDbCollectionField, index: number) => {
        return (
          <Popconfirm
            placement="top"
            icon={null}
            title={() => {
              if (text && isJsonString(text)) {
                return (
                  <div style={{ width: '100rem', height: '20rem', backgroundColor: '#eee' }}>
                    <ReactJson src={JSON.parse(text)} />
                  </div>
                );
              } else {
                return <div style={{ width: '100rem' }}>{text}</div>;
              }
            }}
            showCancel={false}
          >
            <div
              style={{
                cursor: 'pointer',
                color: '#0e73ff',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
                width: '500px',
                overflow: 'hidden',
              }}
            >
              {text}
            </div>
          </Popconfirm>
        );
      },
    },
    {
      title: '操作',
      width: 50,
      key: 'option',
      valueType: 'option',
      render: (dom: React.ReactNode, entity: VectorDbCollectionField) => [
        <Popconfirm
          title="删除提示"
          description="您确定要删除该项数据吗?"
          onConfirm={() => handlerDelete([entity.id])}
          okText="确定"
          cancelText="取消"
        >
          <Button type="text" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];
  const onSearch: SearchProps['onSearch'] = (value, _e, _info) => {
    setCurrentSearchText(value);
    setVectorDbCollectionFiledList([]);
    queryDbFiled(value, '', true);
  };
  const queryDbFiled = (searchText?: string, colName?: string, useSearchText = false) => {
    const collectionName = vectorDbCollectionList.find((item) => item.checked)?.name || '';
    setLoading(true);
    querySemantics({
      text: useSearchText ? searchText : currentSearchText,
      collectionName: colName ? colName : collectionName,
      pageSize: 10,
      current: currentPage,
    })
      .then((resp) => {
        const { code, data } = resp;
        setLoading(false);
        if (200 === code) {
          const dataList = data.data;
          setHasMore(dataList.length > 0);
          if (dataList.length > 0) {
            setVectorDbCollectionFiledList(dataList);
          }
        }
      })
      .catch(() => setLoading(false));
  };

  const onPickerVectorDbCollection = (record: VectorDbCollection) => {
    const newList = vectorDbCollectionList.map((item) => {
      return { ...item, checked: item.id === record.id };
    });
    setCurrentPage(0);
    setVectorDbCollectionList(newList);
    setVectorDbCollectionFiledList([]);
    queryDbFiled(currentSearchText, record.name);
  };

  const handlerPagePre = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
      queryDbFiled();
    }
  };

  const handlerPageNext = () => {
    if (hasMore) {
      setCurrentPage(currentPage + 1);
      queryDbFiled();
    }
  };
  return (
    <div className={styles.semanticsBox}>
      <div className={styles.searchBox}>
        <div className={styles.vectorDbCollectionsBox}>
          <Flex wrap gap="small">
            {vectorDbCollectionList?.map((item: VectorDbCollection, index: number) => {
              return (
                <div
                  key={index}
                  className={classNames(styles.vectorDbCollection, {
                    [styles.vectorDbCollectionActive]: item.checked,
                  })}
                  onClick={() => onPickerVectorDbCollection(item)}
                >
                  <div>
                    <AntDesignOutlined style={{ color: '#0e73ff' }} />
                  </div>
                  <div className={styles.vectorDbCollectionName} title="集合名称">
                    {item.name}
                  </div>
                </div>
              );
            })}
          </Flex>
        </div>
        <div className={styles.search}>
          <Search placeholder="请输入搜索内容" onSearch={onSearch} enterButton />
        </div>
      </div>
      <div className={styles.semanticsBoxContent}>
        <ProTable<VectorDbCollectionField>
          loading={loading}
          style={{ width: '100%', height: '30rem' }}
          scroll={{ x: 'max-content', y: 410 }}
          rowSelection={{
            selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          }}
          tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
            return (
              <Space size={24}>
                <div>
                  已选 {selectedRowKeys.length} 项
                  <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                    取消选择
                  </a>
                </div>
              </Space>
            );
          }}
          tableAlertOptionRender={({ selectedRowKeys }) => {
            return (
              <Space size={16}>
                <Popconfirm
                  title="删除提示"
                  description="您确定要删除这些数据吗?"
                  onConfirm={() => handlerDelete(selectedRowKeys as string[])}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="text" danger>
                    批量删除
                  </Button>
                </Popconfirm>
              </Space>
            );
          }}
          dataSource={vectorDbCollectionFiledList}
          rowKey="id"
          pagination={false}
          columns={columns}
          search={false}
          dateFormatter="string"
          headerTitle="数据列表"
        />
        <div className={styles.proTablePagination}>
          <div className={styles.proTablePaginationP}>
            <Button type="text">当前页共 {vectorDbCollectionFiledList.length} 条数据</Button>
          </div>
          <div className={styles.proTablePaginationP}>
            <Button type="primary" onClick={() => handlerPagePre()}>
              上一页
            </Button>
          </div>
          <div className={styles.proTablePaginationN}>
            <Button disabled={!hasMore} onClick={() => handlerPageNext()}>
              下一页
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SemanticsPage;
