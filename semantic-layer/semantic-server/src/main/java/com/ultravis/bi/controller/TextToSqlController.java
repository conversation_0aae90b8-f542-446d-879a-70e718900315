package com.ultravis.bi.controller;

import com.ultravis.bi.semantic.DataLoadResult;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import com.ultravis.bi.service.DataLoaderService;
import com.ultravis.bi.service.TextToSqlServiceImpl;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v2/semantic")
public class TextToSqlController {

    private final TextToSqlServiceImpl textToSqlService;
    private final DataLoaderService dataLoaderService;

    public TextToSqlController(TextToSqlServiceImpl textToSqlService, DataLoaderService dataLoaderService) {
        this.textToSqlService = textToSqlService;
        this.dataLoaderService = dataLoaderService;
    }

    public record GenSqlReq(String question) {
    }

    public record ExecSqlReq(String question) {
    }

    public record ParseReq(String sql) {
    }

    public record LoadDataReq(String sql) {
    }

    @PostMapping("/gen-sql")
    public ParseResult parseSemanticSql(@RequestBody GenSqlReq genSqlReq) {
        return textToSqlService.genSemanticSql(genSqlReq.question());
    }

    @PostMapping("/parse-sql")
    public ParseResult parseSemanticSql(@RequestBody ParseReq parseReq) {
        return textToSqlService.parseSemanticSql(parseReq.sql());
    }

    @PostMapping("/exec-sql")
    public DataLoadResult execSql(@RequestBody ExecSqlReq execSqlReq) {
        return textToSqlService.ask(execSqlReq.question());
    }

}
