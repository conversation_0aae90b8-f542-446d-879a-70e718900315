package com.ultravis.bi.semantic.rewrite.rewriter;

import java.util.Date;
import java.util.List;

import static java.util.Arrays.asList;

public class Schemas {

    static final List<SemanticModel> schemas = asList(new SemanticModel("Sales1", null,
                    "select product_id, amount from db.sales where 1=1",
                    asList(new SemanticColumn("amount", Double.class, "amount"),
                            new SemanticColumn("productId", String.class, "product_id"))),
            new SemanticModel("Orders", "db.orders", null,
                    asList(new SemanticColumn("totalPrice", Double.class, "total_price"),
                            new SemanticColumn("orderId", String.class, "id"),
                            new SemanticColumn("status", String.class, "order_status"),
                            new SemanticColumn("canRefund", Boolean.class, null,
                                    "CASE WHEN status = 'completed' THEN true ELSE false END",
                                    "Dimension", null),
                            new SemanticColumn("isGoldenDeal", Boolean.class, null,
                                    "CASE WHEN totalPrice > 1000 THEN true ELSE false END",
                                    "Dimension", null))),
            new SemanticModel("order_line_items", "db.order_line_items", null,
                    asList(new SemanticColumn("price", Double.class, "price"),
                            new SemanticColumn("orderId", String.class, "order_id"),
                            new SemanticColumn("productId", String.class, "product_id"))),
            new SemanticModel("Sales", "Sales", null,
                    asList(new SemanticColumn("amount", Double.class, "sales_amount"),
                            new SemanticColumn("cost", Double.class, "sales_cost"),
                            new SemanticColumn("profit", Double.class, null, "amount - cost",
                                    "Dimension", null),
                            new SemanticColumn("productId", String.class, "product_id"),
                            new SemanticColumn("salesDepartment", String.class, "sales_department"),
                            new SemanticColumn("isStarProduct", Boolean.class, null,
                                    "CASE WHEN amount > 100 THEN true ELSE false END", "Dimension",
                                    null),
                            new SemanticColumn("salesDate", Date.class, "sales_date"),
                            new SemanticColumn("quarter", String.class, null,
                                    "to_char(salesDate, 'YYYY-MM')", "Dimension", null))),
            new SemanticModel("Products", "Products", null,
                    asList(new SemanticColumn("id", String.class, "id"),
                            new SemanticColumn("name", String.class, "prod_name"),
                            new SemanticColumn("price", Double.class, "unit_price"))),
            new SemanticModel("ViewSales", "Sales", null,
                    asList(new SemanticColumn("totalSales", Double.class, "salesAmount", null,
                                    "metrics", "sum"),
                            new SemanticColumn("salesAmount", Double.class, "sales_amount"),
                            new SemanticColumn("goodSales", Integer.class, "productId", null,
                                    "metrics", "count", "salesAmount > 100"),
                            new SemanticColumn("totalCount", Integer.class, "productId", null,
                                    "metrics", "count", null),
                            new SemanticColumn("avgSales", Integer.class, null, null, "metrics",
                                    null, null, "totalSales / totalCount"),
                            new SemanticColumn("productId", String.class, "product_id"))));


    static final List<SemanticModel> schemas2 =
            // @formatter:off
            asList(new SemanticModel("CustomerInfo", null, """
                    SELECT * FROM customer c
                    LEFT JOIN nation n ON c.c_nationKey = n.n_nationKey
                    """, asList(new SemanticColumn("customerId", Integer.class, "c_custkey"),
                    new SemanticColumn("customer_name", String.class, "c_name"),
                    new SemanticColumn("address", String.class, "c_address"),
                    new SemanticColumn("nationKey", Integer.class, "c_nationkey"),
                    new SemanticColumn("phone", String.class, "c_phone"),
                    new SemanticColumn("accountBalance", Double.class, "c_acctbal"),
                    new SemanticColumn("marketSegment", String.class, "c_mktsegment"),
                    new SemanticColumn("customer_comment", String.class, "c_comment"),
                    new SemanticColumn("nation_name", String.class, "n_name"),
                    new SemanticColumn("regionKey", Integer.class, "n_regionkey"),
                    new SemanticColumn("nation_comment", String.class, "n_comment"))),
            new SemanticModel("Customer", "customer", null,
                    asList(new SemanticColumn("customerId", Integer.class, "c_custkey"),
                            new SemanticColumn("name", String.class, "c_name"),
                            new SemanticColumn("address", String.class, "c_address"),
                            new SemanticColumn("nationKey", Integer.class, "c_nationkey"),
                            new SemanticColumn("phone", String.class, "c_phone"),
                            new SemanticColumn("accountBalance", Double.class, "c_acctbal"),
                            new SemanticColumn("marketSegment", String.class, "c_mktsegment"),
                            new SemanticColumn("comment", String.class, "c_comment"))),

            new SemanticModel("LineItem", "lineitem", null,
                    asList(new SemanticColumn("orderId", Integer.class, "l_orderkey"),
                            new SemanticColumn("partId", Integer.class, "l_partkey"),
                            new SemanticColumn("supplierId", Integer.class, "l_suppkey"),
                            new SemanticColumn("lineNumber", Integer.class, "l_linenumber"),
                            new SemanticColumn("quantity", Double.class, "l_quantity"),
                            new SemanticColumn("extendedPrice", Double.class, "l_extendedprice"),
                            new SemanticColumn("discount", Double.class, "l_discount"),
                            new SemanticColumn("tax", Double.class, "l_tax"),
                            new SemanticColumn("returnFlag", String.class, "l_returnflag"),
                            new SemanticColumn("lineStatus", String.class, "l_linestatus"),
                            new SemanticColumn("shipDate", Date.class, "l_shipdate"),
                            new SemanticColumn("commitDate", Date.class, "l_commitdate"),
                            new SemanticColumn("receiptDate", Date.class, "l_receiptdate"),
                            new SemanticColumn("shipInstruction", String.class, "l_shipinstruct"),
                            new SemanticColumn("shipMode", String.class, "l_shipmode"),
                            new SemanticColumn("comment", String.class, "l_comment"))),

            new SemanticModel("Nation", "nation", null,
                    asList(new SemanticColumn("nationKey", Integer.class, "n_nationkey"),
                            new SemanticColumn("name", String.class, "n_name"),
                            new SemanticColumn("regionKey", Integer.class, "n_regionkey"),
                            new SemanticColumn("comment", String.class, "n_comment"))),

            new SemanticModel("Orders", "orders", null,
                    asList(new SemanticColumn("orderId", Integer.class, "o_orderkey"),
                            new SemanticColumn("customerId", Integer.class, "o_custkey"),
                            new SemanticColumn("status", String.class, "o_orderstatus"),
                            new SemanticColumn("totalPrice", Double.class, "o_totalprice"),
                            new SemanticColumn("isGoodSales", Boolean.class, null,
                                    "CASE WHEN totalPrice > 500 THEN true ELSE false END",
                                    "Dimension", null),
                            new SemanticColumn("orderDate", Date.class, "o_orderdate"),
                            new SemanticColumn("orderPriority", String.class, "o_orderpriority"),
                            new SemanticColumn("clerk", String.class, "o_clerk"),
                            new SemanticColumn("shipPriority", Integer.class, "o_shippriority"),
                            new SemanticColumn("comment", String.class, "o_comment"),
                            new SemanticColumn("canRefund", Boolean.class, null,
                                    "CASE WHEN status = 'Completed' THEN true ELSE false END",
                                    "Dimension", null))),

            new SemanticModel("Part", "part", null,
                    asList(new SemanticColumn("partId", Integer.class, "p_partkey"),
                            new SemanticColumn("name", String.class, "p_name"),
                            new SemanticColumn("manufacturer", String.class, "p_mfgr"),
                            new SemanticColumn("brand", String.class, "p_brand"),
                            new SemanticColumn("type", String.class, "p_type"),
                            new SemanticColumn("size", Integer.class, "p_size"),
                            new SemanticColumn("container", String.class, "p_container"),
                            new SemanticColumn("retailPrice", Double.class, "p_retailprice"),
                            new SemanticColumn("comment", String.class, "p_comment"))),

            new SemanticModel("ViewTotalSales", "lineitem", null,
                    asList(new SemanticColumn("totalSales", Double.class, "extendedPrice", null,
                                    "metrics", "sum"),
                            new SemanticColumn("discountedSales", Double.class,
                                    null, "SUM(extendedPrice * (1 - discount))",  "metrics", null, null, null),
                            new SemanticColumn("orderCount", Integer.class, "orderkey", null,
                                    "metrics", "count"),
                            new SemanticColumn("extendedPrice", Double.class, "l_extendedprice"),
                            new SemanticColumn("orderkey", Integer.class, "l_orderkey"),                     new SemanticColumn("discount", Double.class, "l_discount"),
                            new SemanticColumn("partId", Integer.class, "l_partkey"))));
            // @formatter:on

    static final List<SemanticModel> kustStudentsSchemas =
            // @formatter:off
            asList(new SemanticModel("DWD_XSJBSJZLB", "LY_DW2.DWD_XSJBSJZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("XM", String.class, "XM"),
                            new SemanticColumn("HYZKM", String.class, "HYZKM"),
                            new SemanticColumn("XBM", String.class, "XBM"),
                            new SemanticColumn("SFDSZN", String.class, "SFDSZN"),
                            new SemanticColumn("GJDQM", String.class, "GJDQM"),
                            new SemanticColumn("JGM", String.class, "JGM"),
                            new SemanticColumn("XYDM", String.class, "XYDM"),
                            new SemanticColumn("BJMC", String.class, "BJMC"),
                            new SemanticColumn("TC", String.class, "TC"),
                            new SemanticColumn("AH", String.class, "AH"),
                            new SemanticColumn("YHKH", String.class, "YHKH"),
                            new SemanticColumn("CSRQ", String.class, "CSRQ"),
                            new SemanticColumn("SFZJH", String.class, "SFZJH"),
                            new SemanticColumn("SFZJLXM", String.class, "SFZJLXM"),
                            new SemanticColumn("GATQWM", String.class, "GATQWM"),
                            new SemanticColumn("JKZKM", String.class, "JKZKM"),
                            new SemanticColumn("ZZMMM", String.class, "ZZMMM"),
                            new SemanticColumn("XXM", String.class, "XXM"),
                            new SemanticColumn("MZM", String.class, "MZM"),
                            new SemanticColumn("XYZJM", String.class, "XYZJM"),
                            new SemanticColumn("NJ", String.class, "NJ"),
                            new SemanticColumn("RXNJ", String.class, "RXNJ"),
                            new SemanticColumn("ZYMC", String.class, "ZYMC"),
                            new SemanticColumn("XSLBM", String.class, "XSLBM"),
                            new SemanticColumn("YXMC", String.class, "YXMC"),
                            new SemanticColumn("XJZT", String.class, "XJZT"),
                            new SemanticColumn("SFZX", String.class, "SFZX"),
                            new SemanticColumn("SFZJ", String.class, "SFZJ"),
                            new SemanticColumn("SFYXJ", String.class, "SFYXJ"))),
                            
            new SemanticModel("DWD_XSSFXXZLB", "LY_DW2.DWD_XSSFXXZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("SFXMMC", String.class, "SFXMMC"),
                            new SemanticColumn("SFQJMC", String.class, "SFQJMC"),
                            new SemanticColumn("XM", String.class, "XM"),
                            new SemanticColumn("LXND", String.class, "LXND"),
                            new SemanticColumn("DWD_XSSFXXZLB_JMJE", double.class, "JMJE", null, "Measure", "sum"),
                            new SemanticColumn("DWD_XSSFXXZLB_TFJE", double.class, "TFJE", null, "Measure", "sum"),
                            new SemanticColumn("DWD_XSSFXXZLB_QFJE", double.class, "QFJE", null, "Measure", "sum"),
                            new SemanticColumn("DWD_XSSFXXZLB_SJJE", double.class, "SJJE", null, "Measure", "sum"),
                            new SemanticColumn("DWD_XSSFXXZLB_DKJE", double.class, "DKJE", null, "Measure", "sum"),
                            new SemanticColumn("DWD_XSSFXXZLB_YJJE", double.class, "YJJE", null, "Measure", "sum"))),
                            
            new SemanticModel("DWD_XSCCZLB", "LY_DW2.DWD_XSCCZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("WJJK", String.class, "WJJK"),
                            new SemanticColumn("CFZTM", String.class, "CFZTM"),
                            new SemanticColumn("CFMCM", String.class, "CFMCM"),
                            new SemanticColumn("CLBM", String.class, "CLBM"),
                            new SemanticColumn("CFYY", String.class, "CFYY"),
                            new SemanticColumn("CFGYR", String.class, "CFGYR"),
                            new SemanticColumn("CFCXWH", String.class, "CFCXWH"),
                            new SemanticColumn("SWHSYJL", String.class, "SWHSYJL"),
                            new SemanticColumn("WJRQ", String.class, "WJRQ"),
                            new SemanticColumn("CFRQ", String.class, "CFRQ"))),
                            
            new SemanticModel("DWD_SSWGWGXXSJB", "LY_DW2.DWD_SSWGWGXXSJB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("JCFX", String.class, "JCFX"),
                            new SemanticColumn("DQBZ", String.class, "DQBZ"),
                            new SemanticColumn("YGSSJ", Date.class, "YGSSJ"),
                            new SemanticColumn("WJFS", String.class, "WJFS"),
                            new SemanticColumn("JCSJ", Date.class, "JCSJ"))),
                            
            new SemanticModel("DWD_SQ_MJXXSJZLB", "LY_DW2.DWD_SQ_MJXXSJZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("JCFX", String.class, "JCFX"),
                            new SemanticColumn("JCSJ", Date.class, "JCSJ"),
                            new SemanticColumn("DQBZ", String.class, "DQBZ"))),
                            
            new SemanticModel("DWD_TSGMJTGXXSJZLB", "LY_DW2.DWD_TSGMJTGXXSJZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("ZJBH", String.class, "ZJBH"),
                            new SemanticColumn("ZJMC", String.class, "ZJMC"),
                            new SemanticColumn("FX", String.class, "FX"),
                            new SemanticColumn("TSG", String.class, "TSG"),
                            new SemanticColumn("XSLB", String.class, "XSLB"),
                            new SemanticColumn("TGSJ", Date.class, "TGSJ"),
                            new SemanticColumn("SKFS", String.class, "SKFS"))),
                            
            new SemanticModel("DWD_QJXXZLB", "LY_DW2.DWD_QJXXZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("XJSJ", String.class, "XJSJ"),
                            new SemanticColumn("QJLX", String.class, "QJLX"),
                            new SemanticColumn("QJZT", String.class, "QJZT"),
                            new SemanticColumn("SFLX", String.class, "SFLX"),
                            new SemanticColumn("JJLXRDH", String.class, "JJLXRDH"),
                            new SemanticColumn("JSSJ", Date.class, "JSSJ"),
                            new SemanticColumn("QJNR", String.class, "QJNR"),
                            new SemanticColumn("KSSJ", Date.class, "KSSJ")
                                    .setSql("TO_DATE(KSSJ, 'yyyy-MM-dd')")
                                    .setExpr(null)
                                    .setComputedInNative(true),
                            new SemanticColumn("QJSJ", Date.class, "QJSJ"))),
                            
            new SemanticModel("DWD_XJYDSJL", "LY_DW2.DWD_XJYDSJL", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("YDYY", String.class, "YDYY"),
                            new SemanticColumn("YDLXMC", String.class, "YDLXMC"),
                            new SemanticColumn("YDSJ", String.class, "YDSJ"))),
                            
            new SemanticModel("DWD_XSJLZLB", "LY_DW2.DWD_XSJLZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("JLMC", String.class, "JLMC"),
                            new SemanticColumn("HJSJ", String.class, "HJSJ"),
                            new SemanticColumn("HJXQ", String.class, "HJXQ"),
                            new SemanticColumn("BJDW", String.class, "BJDW"),
                            new SemanticColumn("JLJE", double.class, "JLJE"),
                            new SemanticColumn("JLJBM", String.class, "JLJBM"),
                            new SemanticColumn("JLYY", String.class, "JLYY"),
                            new SemanticColumn("HJXND", String.class, "HJXND"),
                            new SemanticColumn("JLDJM", String.class, "JLDJM"))),
                            
            new SemanticModel("DWD_ZHCPCJXXZLB", "LY_DW.DWD_ZHCPCJXXZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("XQ", String.class, "XQ"),
                            new SemanticColumn("DWD_ZHCPCJXXZLB_CJ", double.class, "CJ"),
                            new SemanticColumn("DWD_ZHCPCJXXZLB_BJPM", double.class, "BJPM"),
                            new SemanticColumn("DWD_ZHCPCJXXZLB_ZYPM", double.class, "ZYPM"),
                            new SemanticColumn("DWD_ZHCPCJXXZLB_CJ_sum", double.class, "DWD_ZHCPCJXXZLB_CJ", null, "Measure", "sum"),
                            new SemanticColumn("DWD_ZHCPCJXXZLB_BJPM_max", double.class, "DWD_ZHCPCJXXZLB_BJPM", null, "Measure", "max"),
                            new SemanticColumn("DWD_ZHCPCJXXZLB_ZYPM_max", double.class, "DWD_ZHCPCJXXZLB_ZYPM", null, "Measure", "max"))
            ),
                            
            new SemanticModel("DWD_ZHCPZBCJZLB", "LY_DW2.DWD_ZHCPZBCJZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("CPLXMC", String.class, "CPLXMC"),
                            new SemanticColumn("XNXQ", String.class, "XNXQ"),
                            new SemanticColumn("DWD_ZHCPZBCJZLB_PCFS", double.class, "PCFS", null, "Measure", "sum"))),
                            
            new SemanticModel("kustBKSJSXX", null, 
                    "select t1.XGH as XH,t2.TSMC,t2.JYSJ,t2.SJGHSJ\r\nfrom LY_DW2.DWD_DZJBSJZLB t1 \r\njoin LY_DW2.DWD_TSLSJYSJZLB t2 on t1.JSZH = t2.JSZH and t1.DZZT = '有效' and t1.DZLBMC = '本科生'",
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("TSMC", String.class, "TSMC"),
                            new SemanticColumn("JYSJ", String.class, "JYSJ"),
                            new SemanticColumn("SJGHSJ", String.class, "SJGHSJ"))),
                            
            new SemanticModel("kustXSQDXX", null, 
                    "select t1.XH,t1.QDSJ,t1.QDJG,t2.BT,t2.NR,t1.FZMC,t2.XQMC\r\nfrom LY_DW2.DWD_XSQDXXZLB t1\r\njoin LY_DW2.DWD_QDNRFBXXZLB t2 on t1.QDID = t2.WYBS",
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("QDSJ", Date.class, "QDSJ"),
                            new SemanticColumn("QDJG", String.class, "QDJG"),
                            new SemanticColumn("BT", String.class, "BT"),
                            new SemanticColumn("NR", String.class, "NR"),
                            new SemanticColumn("FZMC", String.class, "FZMC"),
                            new SemanticColumn("XQMC", String.class, "XQMC"))),
                            
            new SemanticModel("kustXSKSCJXX", null, 
                    "select t1.XH,t1.XNXQ,t1.SFCXS,\r\n  t2.KCMC,t2.XF KCXF,t2.ZXS KCZXS,t2.MZXS KCMZXS,t2.KCFZR,\r\n  t3.JD,t3.PSCJ,t3.KCCJ,t3.ZCJ,t3.PJXFJD,t3.DJLKSCJ,t3.FSLKSCJ,t3.KSRQ,t3.SFJG\r\nfrom LY_DW2.DWD_XKSJLB t1\r\njoin LY_DW2.DWD_KCSJLB t2 on t1.KCH = t2.KCH\r\njoin LY_DW2.DWD_CJZLB t3 on t1.XH = t3.XH and t1.KCH = t3.KCH",
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("XNXQ", String.class, "XNXQ"),
                            new SemanticColumn("SFCXS", String.class, "SFCXS"),
                            new SemanticColumn("KCMC", String.class, "KCMC"),
                            new SemanticColumn("KCFZR", String.class, "KCFZR"),
                            new SemanticColumn("KSRQ", String.class, "KSRQ"),
                            new SemanticColumn("SFJG", String.class, "SFJG"),
                            new SemanticColumn("kustXSKSCJXX_KCXF", double.class, "KCXF", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_KCZXS", double.class, "KCZXS", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_KCMZXS", double.class, "KCMZXS", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_JD", double.class, "JD", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_PSCJ", double.class, "PSCJ", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_KCCJ", double.class, "KCCJ", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_ZCJ", double.class, "ZCJ", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_PJXFJD", double.class, "PJXFJD", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_DJLKSCJ", double.class, "DJLKSCJ", null, "Dimension", null),
                            new SemanticColumn("kustXSKSCJXX_FSLKSCJ", double.class, "FSLKSCJ", null, "Dimension", null),

                            new SemanticColumn("kustXSKSCJXX_KCXF_sum", double.class, "kustXSKSCJXX_KCXF", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_KCZXS_sum", double.class, "kustXSKSCJXX_KCZXS", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_KCMZXS_sum", double.class, "kustXSKSCJXX_KCMZXS", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_JD_sum", double.class, "kustXSKSCJXX_JD", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_PSCJ_sum", double.class, "kustXSKSCJXX_PSCJ", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_KCCJ_sum", double.class, "kustXSKSCJXX_KCCJ", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_ZCJ_sum", double.class, "kustXSKSCJXX_ZCJ", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_PJXFJD_sum", double.class, "kustXSKSCJXX_PJXFJD", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_DJLKSCJ_sum", double.class, "kustXSKSCJXX_DJLKSCJ", null, "Measure", "sum"),
                            new SemanticColumn("kustXSKSCJXX_FSLKSCJ_sum", double.class, "kustXSKSCJXX_FSLKSCJ", null, "Measure", "sum")
                    )),
                            
            new SemanticModel("kustBKSXFXX", null, 
                    "select\r\n    t1.XGH XH,t1.ZJH,t1.ZHYE,t1.KH,t1.KNYE,t1.ZHKHRQ,t1.ZHYXRQ,t1.ZHZT,\r\n\t\tt2.ZDZH,t2.JYJE,t2.RZRQSJ,t2.YKCS,\r\n\t\tt3.DZ,t3.SHMC\r\nfrom LY_DW2.DWD_YKTZHXXZLB t1\r\njoin LY_DW2.DWD_YKTXFXXZLB t2 on t1.ZH = t2.ZH\r\njoin LY_DW2.DWD_YKTSHXXZLB t3 on t2.ZDZH = t3.ZH",
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("KH", String.class, "KH"),
                            new SemanticColumn("ZHKHRQ", Date.class, "ZHKHRQ"),
                            new SemanticColumn("ZHYXRQ", Date.class, "ZHYXRQ"),
                            new SemanticColumn("ZHZT", String.class, "ZHZT"),
                            new SemanticColumn("RZRQSJ", Date.class, "RZRQSJ"),
                            new SemanticColumn("DZ", String.class, "DZ"),
                            new SemanticColumn("SHMC", String.class, "SHMC"),
                            new SemanticColumn("kustBKSXFXX_ZHYE", double.class, "ZHYE", null, "Measure", "sum"),
                            new SemanticColumn("kustBKSXFXX_KNYE", double.class, "KNYE", null, "Measure", "sum"),
                            new SemanticColumn("kustBKSXFXX_JYJE", double.class, "JYJE", null, "Measure", "sum"),
                            new SemanticColumn("kustBKSXFXX_YKCS", double.class, "YKCS", null, "Measure", "sum"))),
                            
            new SemanticModel("DWD_XSZSXXSJZLB", "LY_DW2.DWD_XSZSXXSJZLB", null,
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("XQMC", String.class, "XQMC"),
                            new SemanticColumn("LC", String.class, "LC"),
                            new SemanticColumn("RZSJ", Date.class, "RZSJ"),
                            new SemanticColumn("CWH", String.class, "CWH"),
                            new SemanticColumn("FJH", String.class, "FJH"),
                            new SemanticColumn("LD", String.class, "LD"),
                            new SemanticColumn("ZT", String.class, "ZT"))),
                            
            new SemanticModel("DWD_XSZZZLB", null, 
                    "SELECT T.XH,T.XXSQBZ,T.DQZT ZZDQZT,T.XQ  ZZXQ, T.PROJECT_NAME,T.ZZJE FROM LY_DW2.DWD_XSZZZLB T",
                    asList(new SemanticColumn("XH", String.class, "XH").setPrimaryKey(true),
                            new SemanticColumn("XXSQBZ", String.class, "XXSQBZ"),
                            new SemanticColumn("ZZDQZT", String.class, "ZZDQZT"),
                            new SemanticColumn("ZZXQ", String.class, "ZZXQ"),
                            new SemanticColumn("PROJECT_NAME", String.class, "PROJECT_NAME"),
                            new SemanticColumn("DWD_XSZZZLB_ZZJE", double.class, "ZZJE", null, "Measure", "sum"))));
            // @formatter:on

    public static final List<SemanticRelationship> kustStudentRelationships = asList(
            new SemanticRelationship("DWD_XJYDSJL", "DWD_XSJBSJZLB", "inner join", "DWD_XJYDSJL.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("kustBKSXFXX", "DWD_XSJBSJZLB", "inner join", "kustBKSXFXX.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_QJXXZLB", "DWD_XSJBSJZLB", "inner join", "DWD_QJXXZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_TSGMJTGXXSJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_TSGMJTGXXSJZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_SQ_MJXXSJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_SQ_MJXXSJZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSSFXXZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSSFXXZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_SSWGWGXXSJB", "DWD_XSJBSJZLB", "inner join", "DWD_SSWGWGXXSJB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSCCZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSCCZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("kustXSQDXX", "DWD_XSJBSJZLB", "inner join", "kustXSQDXX.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_ZHCPZBCJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_ZHCPZBCJZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_ZHCPCJXXZLB", "DWD_XSJBSJZLB", "left join", "DWD_ZHCPCJXXZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("kustXSKSCJXX", "DWD_XSJBSJZLB", "inner join", "kustXSKSCJXX.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSJLZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSJLZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSZZZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSZZZLB.XH = DWD_XSJBSJZLB.XH"),
            new SemanticRelationship("DWD_XSZSXXSJZLB", "DWD_XSJBSJZLB", "inner join", "DWD_XSZSXXSJZLB.XH = DWD_XSJBSJZLB.XH")
    );
}
