//导出聊天消息
import {exportCsvFile} from "../../../utils/utils";
import {queryResultToDashboardApi} from "../../../service";
import {message} from "antd";

export function exportChatMsg(queryResults: any, queryColumns: any) {
    if (!!queryResults) {
        const exportData = queryResults.map(item => {
            return Object.keys(item).reduce((result, key) => {
                const columnName = queryColumns?.find(column => column.nameEn === key)?.name || key;
                result[columnName] = item[key];
                return result;
            }, {});
        });
        exportCsvFile(exportData);
    }
}

//添加到看板
export function handlerAddDashboard(chatContext: any) {
    console.log("添加到看板", chatContext);
    if (!chatContext) {
        return;
    }
    const {dimensions, metrics, id, queryId, dateInfo, dimensionFilters, properties} = chatContext || {};
    const chatContextValue = {
        dimensions,
        metrics,
        dateInfo,
        dimensionFilters,
        parseId: id,
        queryId,
    };
    const queryText = properties.CONTEXT.llmReq.queryText;
    const formData = {
        title: queryText,
        chatQueryParams: JSON.stringify(chatContextValue),
        status: 1
    }
    queryResultToDashboardApi(formData).then((resp: any) => {
        const {code, msg} = resp;
        if (200 !== code) {
            message.error("添加失败:【" + msg + "】")
            return
        }
        message.success("添加成功")
    });
}
