.mobileTableCard {
 border: 1px solid #DBDBDB;
 border-radius: 0.5rem;
 margin-bottom: 1rem;
 background-color: white;

 .mobileTableCardAction {
  height: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #6B758B;

  .mobileTableCardActionIcon {
   margin-top: 0.1rem;
  }
 }

 .mobileTableCardBody {
  border-top-left-radius: 0.5rem;

  .mobileTableCardBodyRowDivider {
   position: relative;
   z-index: 10;
   height: 0.01rem;
   background-color: #DBDBDB;
   margin-left: 1rem;
   margin-right: 1rem;
   margin-top: -1px;
  }

  .mobileTableCardBodyRow {
   min-height: 2rem;
   display: flex;
   align-items: center;

   .mobileTableCardBodyRowValue {
    width: 70%;
    padding-left: 1rem;
    color: #832B24;
   }

   .mobileTableCardBodyRowLabel {
    padding: 1rem 0 1rem 0;
    color: #832B24;
    background-color: rgba(189, 76, 67, 0.20);
    min-height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30%;
   }
  }
 }
}
