name: supersonic windows CI

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: windows-latest  # Specify a Windows runner

    strategy:
      matrix:
        java-version: [8, 11, 21]  # Add JDK 21 to the matrix

    steps:
      - uses: actions/checkout@v2

      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v2
        with:
          java-version: ${{ matrix.java-version }}
          distribution: 'adopt'  # You might need to change this if 'adopt' doesn't support JDK 21

      - name: Cache Maven packages
        uses: actions/cache@v2
        with:
          path: ~\.m2  # Windows uses a backslash for paths
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build with Maven
        run: mvn -B package --file pom.xml

      - name: Test with Maven
        run: mvn test