package com.ultravis.bi.infra.schema;

import com.tencent.supersonic.common.pojo.ModelRela;
import com.tencent.supersonic.common.pojo.enums.FilterOperatorEnum;
import com.tencent.supersonic.headless.api.pojo.Dimension;
import com.tencent.supersonic.headless.api.pojo.ModelDetail;
import com.tencent.supersonic.headless.api.pojo.SchemaElement;
import com.tencent.supersonic.headless.api.pojo.enums.DimensionType;
import com.tencent.supersonic.headless.api.pojo.response.ModelResp;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticColumn;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticModel;
import com.ultravis.bi.semantic.rewrite.rewriter.SemanticRelationship;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class Schemas {

    public static List<SemanticModel> toSemanticModels(List<ModelResp> supersonicModels, ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper) {
        return supersonicModels.stream().map(it -> toSemanticModel(it, dbTypeToJavaTypeMapper)).toList();
    }

    public static List<SemanticRelationship> toSemanticRelationships(List<ModelResp> supersonicModels, List<ModelRela> relationships) {
        return relationships.stream().map(it -> {
            String fromModelName = getModelName(supersonicModels, it.getFromModelId());
            String toModelName = getModelName(supersonicModels, it.getToModelId());
            return new SemanticRelationship(
                    fromModelName,
                    toModelName,
                    it.getJoinType(),
                    getJoinConditionExpr(it, fromModelName, toModelName)
            );
        }).toList();
    }

    private static SemanticModel toSemanticModel(ModelResp supersonicModel, ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper) {
        SemanticModel model = new SemanticModel();
        model.setName(supersonicModel.getBizName());
        ModelDetail modelDetail = supersonicModel.getModelDetail();
        if (modelDetail.getQueryType().equals("table_query")) {
            model.setExpr(modelDetail.getTableQuery());
        } else if (modelDetail.getQueryType().equals("sql_query")) {
            model.setSql(modelDetail.getSqlQuery());
        } else {
            throw new RuntimeException("Unsupported query type: " + modelDetail.getQueryType());
        }

        Stream<SemanticColumn> identifierStream = modelDetail.getIdentifiers().stream()
                .filter(identifier -> identifier.getType().equals("primary"))
                .map(identifier -> {
                    SemanticColumn col = new SemanticColumn();
                    col.setName(identifier.getBizName());
                    // TODO 支持 primary key 类型?
                    col.setSemanticType("Dimension");
                    col.setType(String.class);
                    col.setExpr(identifier.getBizName());
                    col.setSemanticModel(model);
                    col.setPrimaryKey(true);
                    return col;
                });

        Stream<SemanticColumn> dimensionStream = modelDetail.getDimensions().stream().map(dim -> {
            SemanticColumn col = new SemanticColumn();
            col.setName(dim.getBizName());
            col.setSemanticType("Dimension");
            Class<?> dataTypeClass = columnTypeToJavaType(dbTypeToJavaTypeMapper, modelDetail, dim);
            if (dataTypeClass == null) {
                log.warn("忽略字段 '{}', 因为遇到不支持的数据类型.", dim.getBizName());
                return null;
            }
            // TODO REMOVE ME. 取不到前端修改后的字段信息.
            if (model.getName().equals("DWD_QJXXZLB")) {
                System.out.println();
            }
            col.setType(dataTypeClass);
            col.setFormat(dim.getDateFormat());
            if (dim.getExpr() != null) {
                col.setSql(dim.getExpr());
            } else {
                if (DimensionType.isTimeDimension(dim.getType())
                        && rawColumnTypeIsA(dbTypeToJavaTypeMapper, dim, modelDetail, String.class)) {
                    String dateFormat = dim.getDateFormat();
                    if (dateFormat == null || dateFormat.isBlank()) {
                        dateFormat = "yyyy-MM-dd";
                    }
                    String computing = getToDateExpr(dim.getBizName(), dateFormat);
                    col.setSql(computing);
                    col.setExpr(null);
                    col.setComputedInNative(true);
                } else {
                    col.setExpr(dim.getBizName());
                }
            }
            col.setSemanticModel(model);
            return col;
        }).filter(Objects::nonNull);

        // TODO 目前把数值类型自动创建为 Measure, 是错误的, 暂时将错就错.
        Stream<SemanticColumn> dimensionFromMeasureStream = modelDetail.getMeasures().stream().map(measure -> {
            SemanticColumn col = new SemanticColumn();
            col.setName(measure.getBizName());
            col.setSemanticType("Dimension");
            // TODO 确定类型
            col.setType(double.class);
            col.setExpr(measure.getExpr());
            col.setSemanticModel(model);
            return col;
        });

        Stream<SemanticColumn> allDimensionStream = Stream.concat(
                Stream.concat(identifierStream, dimensionStream),
                dimensionFromMeasureStream);

        Stream<SemanticColumn> measureStream = modelDetail.getMeasures().stream().map(measure -> {
            SemanticColumn col = new SemanticColumn();
            col.setName(measure.getBizName() + "_" + measure.getAgg());
            col.setSemanticType("Measure");
            col.setType(double.class);
            col.setAgg(measure.getAgg());
            col.setExpr(measure.getBizName());
            col.setSemanticModel(model);
            return col;
        });

        List<SemanticColumn> columnList = Stream.concat(allDimensionStream, measureStream).toList();
        model.setColumns(columnList);
        return model;
    }

    @NotNull
    public static String getToDateExpr(String bizName, String dateFormat) {
        String[] args = Stream.concat(
                Stream.of(bizName),
                Arrays.stream(dateFormat.split("\\|")).map(it -> "'" + it + "'")
        ).toArray(String[]::new);
        String paramsFormat = String.join(", ", Collections.nCopies(args.length, "%s"));
        // TODO 根据数据库类型设置
        return String.format("TO_DATE(" + paramsFormat + ")", (Object[]) args);
        // return "TO_DATE(" + paramsFormat + ")".formatted((Object[]) args); //TODO 为什么不work?
    }

    private static boolean rawColumnTypeIsA(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, Dimension dim, ModelDetail modelDetail, Class<String> clazz) {
        Class<?> cloCls = rawColumnTypeToJavaType(dbTypeToJavaTypeMapper, modelDetail, dim);
        return cloCls != null && clazz.isAssignableFrom(cloCls);
    }

    public static String toContextStringCSV(List<ModelResp> supersonicModels, List<ModelRela> relationships) {
        StringBuilder sb = new StringBuilder();
        sb.append("There are ").append(supersonicModels.size()).append(" tables:\n");

        sb.append("Table Name,Table Description\n");
        supersonicModels.forEach(model -> {
            String description = model.getDescription() != null ? model.getDescription() : model.getName();
            sb.append(model.getBizName()).append(",").append(description).append("\n");
        });
        sb.append("\n");

        supersonicModels.forEach(model -> {
            sb.append("The structure of table ").append(model.getBizName()).append(" is as follows:\n");
            sb.append("column name,column type,semantic type,column description\n");
            model.getModelDetail().getIdentifiers().forEach(identifier -> {
                String description = identifier.getName();
                if (description.contains(",")) {
                    description = "\"" + description + "\"";
                }
                sb.append(identifier.getBizName()).append(",").append("primary_key").append(",")
                        .append("Dimension").append(",").append(description).append("\n");
            });
            model.getModelDetail().getDimensions().forEach(dim -> {
                String description = dim.getDescription() != null ? dim.getDescription() : dim.getName();
                if (description.contains(",")) {
                    description = "\"" + description + "\"";
                }
                String dataType = getColumnType(model.getModelDetail(), dim);
                sb.append(dim.getBizName()).append(",").append(dataType).append(",")
                        .append("Dimension").append(",").append(description).append("\n");
            });
            /// TODO 目前无脑把数值类型当做 Measure 是不正确的.
            model.getModelDetail().getMeasures().forEach(measure -> {
                String alias = measure.getAlias() != null ? measure.getAlias() : measure.getName();
                if (alias.contains(",")) {
                    alias = "\"" + alias + "\"";
                }
                // TODO 目前无脑把数值类型当做 Measure 是不正确的.
                String semanticType = "Dimension";
                sb.append(measure.getBizName()).append(",").append("number").append(",").append(semanticType).append(",")
                        .append(alias).append("\n");
            });
            model.getModelDetail().getMeasures().forEach(measure -> {
                String alias = measure.getAlias() != null ? measure.getAlias() : measure.getName();
                if (alias.contains(",")) {
                    alias = "\"" + alias + "\"";
                }
                // TODO 目前无脑把数值类型当做 Measure 是不正确的.
                String semanticType = "Measure";
                String bizName = measure.getBizName() + "_" + measure.getAgg();
                alias += " " + measure.getAgg().toUpperCase();
                sb.append(bizName).append(",").append("number").append(",").append(semanticType).append(",")
                        .append(alias).append("\n");
            });
            sb.append("\n");
        });

        sb.append("\n");

        sb.append("The relationships between tables are as follows:\n");
        sb.append("from table,to table,join type,join conditions\n");
        relationships.forEach(rela -> {
            String fromModelName = getModelName(supersonicModels, rela.getFromModelId());
            String toModelName = getModelName(supersonicModels, rela.getToModelId());
            String str = getJoinConditionExpr(rela, fromModelName, toModelName);
            sb.append(fromModelName).append(",").append(toModelName).append(",").append(rela.getJoinType()).append(",")
                    .append(str).append("\n");
        });
        sb.append("\n");

        return sb.toString();
    }

    public static String getJoinConditionExpr(ModelRela rela, String fromModelName, String toModelName) {
        return rela.getJoinConditions().stream()
                .map(cond -> {
                    String leftField = "%s.%s".formatted(fromModelName, cond.getLeftField());
                    String rightField = "%s.%s".formatted(toModelName, cond.getRightField());
                    return toJoinCondExpr(cond.getOperator(), leftField, rightField);
                })
                .collect(Collectors.joining(" AND "));
    }

    private static String toJoinCondExpr(FilterOperatorEnum operator, String leftField, String rightField) {
        return switch (operator) {
            case IN -> "%s IN (%s)".formatted(leftField, rightField);
            case NOT_IN -> "%s NOT IN (%s)".formatted(leftField, rightField);
            case EQUALS -> "%s = %s".formatted(leftField, rightField);
            case BETWEEN -> "%s BETWEEN %s".formatted(leftField, rightField);
            case GREATER_THAN -> "%s > %s".formatted(leftField, rightField);
            case GREATER_THAN_EQUALS -> "%s >= %s".formatted(leftField, rightField);
            case IS_NULL -> "%s IS NULL".formatted(leftField);
            case IS_NOT_NULL -> "%s IS NOT NULL".formatted(leftField);
            case LIKE -> "%s LIKE '%s'".formatted(leftField, rightField);
            case MINOR_THAN -> "%s < %s".formatted(leftField, rightField);
            case MINOR_THAN_EQUALS -> "%s <= %s".formatted(leftField, rightField);
            case NOT_EQUALS -> "%s <> %s".formatted(leftField, rightField);
            case SQL_PART -> leftField;
            case EXISTS -> "EXISTS(%s)".formatted(leftField);
        };
    }

    public static String getModelName(List<ModelResp> supersonicModels, Long modelId) {
        return supersonicModels.stream()
                .filter(m -> m.getId().equals(modelId))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("模型未找到: " + modelId)).getBizName();
    }

    @Nullable
    private static Class<?> columnTypeToJavaType(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, ModelDetail modelDetail, Dimension dim) {
        String dataType = getColumnType(modelDetail, dim);
        return dbTypeToJavaType(dbTypeToJavaTypeMapper, dataType);
    }

    @Nullable
    private static Class<?> rawColumnTypeToJavaType(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, ModelDetail modelDetail, Dimension dim) {
        String dataType = getRawColumnType(modelDetail, dim);
        return dbTypeToJavaType(dbTypeToJavaTypeMapper, dataType);
    }

    @Nullable
    private static Class<?> dbTypeToJavaType(ColumnTypeToJavaTypeMapper dbTypeToJavaTypeMapper, String dataType) {
        Class<?> dataTypeClass;
        try {
            dataTypeClass = dbTypeToJavaTypeMapper.mapToJavaType(dataType);
        } catch (Exception e) {
//            log.warn("不支持的数据类型. Column Name: {}, Biz Name: {}, Data Type: {}. Caused By:", dim.getName(), dim.getBizName(), dataType, e);
            return null;
        }
        return dataTypeClass;
    }

    private static String getColumnType(ModelDetail modelDetail, Dimension dim) {
        if (DimensionType.isTimeDimension(dim.getType())) {
            return "date";
        }
        return getRawColumnType(modelDetail, dim);
    }

    private static String getRawColumnType(ModelDetail modelDetail, Dimension dim) {
        return modelDetail.getFields().stream()
                .filter(f -> f.getFieldName().equals(dim.getFieldName()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到对应的字段: " + dim.getName() + " -> " + dim.getFieldName()))
                .getDataType();
    }

    @NotNull
    public static String getQualifiedDimensionId(List<ModelResp> models, SchemaElement dim) {
        return getModelBizName(dim.getModel(), models) + "." + dim.getBizName();
    }

    public static String getModelBizName(Long modelId, List<ModelResp> key) {
        return getModelName(key, modelId);
    }
}
