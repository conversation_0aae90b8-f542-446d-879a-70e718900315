package com.ultravis.bi.service;

import com.tencent.supersonic.headless.api.pojo.response.DatabaseResp;
import com.tencent.supersonic.headless.server.service.DatabaseService;
import com.ultravis.bi.semantic.DataLoadResult;
import com.ultravis.bi.semantic.DataLoader;
import com.ultravis.bi.semantic.SemanticLangParser;
import com.ultravis.bi.semantic.rewrite.rewriter.ParseResult;
import com.ultravis.bi.text2sql.DefaultTextToSqlGenerator;
import com.ultravis.bi.text2sql.GenSqlContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;

@Slf4j
@Service
public class TextToSqlServiceImpl {

    public static final long DATA_SET_ID = 18L;
    public static final long DATABASE_ID = 2L;

    private final SemanticLangParser semanticLangParser;
    private final DataLoader dataLoader;
    private final DefaultTextToSqlGenerator textToSqlGenerator;
    private final DatabaseService databaseService;
    private final ContextFactory contextFactory;

    public TextToSqlServiceImpl(
            SemanticLangParser semanticLangParser,
            DataLoader dataLoader,
            DefaultTextToSqlGenerator textToSqlGenerator,
            DatabaseService databaseService, ContextFactory contextFactory) {
        this.semanticLangParser = semanticLangParser;
        this.dataLoader = dataLoader;
        this.textToSqlGenerator = textToSqlGenerator;
        this.databaseService = databaseService;
        this.contextFactory = contextFactory;
    }

    public ParseResult genSemanticSql(String question) {
        return genSemanticSql(question, contextFactory.newGenSqlContext(Set.of(DATA_SET_ID)));
    }

    public ParseResult parseSemanticSql(String sql) {
        return semanticLangParser.parseSql(sql, contextFactory.newParseSqlContext(Set.of(DATA_SET_ID)));
    }

    public DataLoadResult ask(String question) {
        GenSqlContext genSqlContext = contextFactory.newGenSqlContext(Set.of(DATA_SET_ID));
        ParseResult parseResult = genSemanticSql(question, genSqlContext);
        DatabaseResp database = databaseService.getDatabase(DATABASE_ID);
        try {
            return dataLoader.loadDataByParsedSql(parseResult, database, genSqlContext.parseSqlContext);
        } catch (Exception e) {
            // TODO 用特定异常包装执行上下文信息
            log.error("语义SQL执行失败: {}", parseResult.getInputSql(), e);
            throw e;
        }
    }

    private ParseResult genSemanticSql(String question, GenSqlContext ctx) {
        return textToSqlGenerator.genSql(
                question,
                ctx.parseSqlContext.supersonicModelsAndRelationships,
                ctx.instructions,
                (sql) -> verifySql(sql, ctx));
    }

    private ParseResult verifySql(String sql, GenSqlContext ctx) {
        return semanticLangParser.parseSql(sql, ctx.parseSqlContext);
    }
}
