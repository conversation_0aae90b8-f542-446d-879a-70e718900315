name: SuperSonic enhancement request
description: Add an enhancement for SuperSonic
title: "[Enhancement] "
labels: enhancement

body:
  - type: markdown
    attributes:
      value: |
        Thank you very much for your good enhancement for SuperSonic.
  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        Please make sure to search in the [issues](https://github.com/tencentmusic/supersonic/issues?q=is%3Aissue) first to see
        whether the same issue was reported already.
      options:
        - label: >
            I had searched in the [issues](https://github.com/tencentmusic/supersonic/issues?q=is%3Aissue) and found no similar
            issues.
          required: true

  - type: textarea
    attributes:
      label: Description
      description: Describe the enhancement what you want, including motivation if it exists.

  - type: textarea
    attributes:
      label: Solution
      placeholder: >
        Add overview of proposed solution. 

        Add related materials like links if they exist.

  - type: checkboxes
    attributes:
      label: Are you willing to submit PR?
      description: >
        We very much look forward to developers or users to help develop the SuperSonic together.
        If you are willing to submit a PR to implement this feature, please tick it.
      options:
        - label: Yes I am willing to submit a PR!

  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: The Code of Conduct helps create a safe space for everyone. We require that everyone agrees to it.
      options:
        - label: >
            I agree to follow this project's
            [Code of Conduct](https://www.apache.org/foundation/policies/conduct)
          required: true

  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
