import {isMobile, jsonParse, setToken, updateMessageContainerScroll, uuid} from '../../utils/utils';
import {
    forwardRef,
    ForwardRefRenderFunction,
    useCallback,
    useEffect,
    useImperativeHandle,
    useRef,
    useState,
} from 'react';
import styles from './style.module.less';
import {AgentType, ConversationDetailType, MessageItem, MessageTypeEnum} from '../type';
import {queryAgentList} from '../service';
import {useThrottleFn} from 'ahooks';
import {cloneDeep, isBoolean} from 'lodash';
import {HistoryMsgItemType, MsgDataType, SendMsgParamsType} from '../../common/type';
import {getHistoryMsg} from '../../service';
import {ConfigProvider} from 'antd';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import MobileChatFooter from '../MobileChatFooter';
import MobileChatPrompts from '../MobileChatPrompts/MobileChatPrompts';
import MobileChatHistory from '../MobileChatHistory/MobileChatHistory';
import MobileFooterTools from "../MobileFooterTools/MobileFooterTools";
import MobileVoiceInput from "../MobileVoiceInput/MobileVoiceInput";
import MobileAgents from "../MobileAgents";
import MessageContainer from "../MessageContainer";
import MobileWelcome from "../MobileWelcome/MobileWelcome";
import PlanModeMessageContainer from "../PlanModeMessageContainer";
import AgentTip from "../components/AgentTip";
import NextChatMessageContainer from "../NextChatMessageContainer";

dayjs.locale('zh-cn');

type Props = {
    token?: string;
    agentIds?: number[];
    initialAgentId?: number;
    chatVisible?: boolean;
    noInput?: boolean;
    isDeveloper?: boolean;
    integrateSystem?: string;
    onCurrentAgentChange?: (agent?: AgentType) => void;
    onReportMsgEvent?: (msg: string, valid: boolean) => void;
    onClickAccessToken: () => void;
    onChangePassword: () => void;
    onClickLoginOut: () => void;
};

const MobileChat: ForwardRefRenderFunction<any, Props> = (
    {
        token,
        agentIds,
        initialAgentId,
        chatVisible,
        noInput,
        isDeveloper,
        integrateSystem,
        onCurrentAgentChange,
        onReportMsgEvent,
        onClickAccessToken,
        onChangePassword,
        onClickLoginOut
    },
    ref
) => {
    const [messageList, setMessageList] = useState<MessageItem[]>([]);
    const [pageNo, setPageNo] = useState(1);
    const [hasNextPage, setHasNextPage] = useState(false);
    const [historyInited, setHistoryInited] = useState(false);
    const [currentConversation, setCurrentConversation] = useState<ConversationDetailType | undefined>(isMobile ? {
        //chatId: 0,
        chatId: 5,
        chatName: '问答'
    } : undefined);
    const [historyVisible, setHistoryVisible] = useState(false);
    const [agentList, setAgentList] = useState<AgentType[]>([]);
    const [currentAgent, setCurrentAgent] = useState<AgentType>();
    const [mobileAgentsVisible, setMobileAgentsVisible] = useState(false);

    const [isDebugMode, setIsDebugMode] = useState<boolean>(true);
    const [loading, setLoading] = useState<boolean>(false);
    const [useRag, setUseRag] = useState<boolean>(false);
    const [useNewVersion, setUseNewVersion] = useState<boolean>(false);
    const [userInputMsg, setUserInputMsg] = useState<string>("");
    const [customMsg, setCustomMsg] = useState<string>("");
    const [chatPerformanceMode, setChatPerformanceMode] = useState<'speed-first' | 'quality-first'>('speed-first');

    const [showChatWelcome, setShowChatWelcome] = useState<boolean>(false);
    const [voiceInputVisible, setVoiceInputVisible] = useState<boolean>(false);
    const [chatPromptsVisible, setChatPromptsVisible] = useState<boolean>(false);
    const [focusSender, setFocusSender] = useState<boolean>(false);


    const conversationRef = useRef<any>();


    useImperativeHandle(ref, () => ({
        sendCopilotMsg,
    }));

    const sendCopilotMsg = (params: SendMsgParamsType) => {
        const {agentId, msg, modelId} = params;
        if (currentAgent?.id !== agentId) {
            setMessageList([]);
            const agent = agentList.find(item => item.id === agentId) || ({} as AgentType);
            updateCurrentAgent({...agent, initialSendMsgParams: params});
        } else {
            onSendMsg(msg, messageList, modelId, params);
        }
    };

    const updateAgentConfigMode = (agent: AgentType) => {
        const toolConfig = jsonParse(agent?.toolConfig, {});
        const {debugMode} = toolConfig;
        if (isBoolean(debugMode)) {
            setIsDebugMode(debugMode);
        } else {
            setIsDebugMode(true);
        }
    };

    const updateCurrentAgent = (agent?: AgentType) => {
        setCurrentAgent(agent);
        onCurrentAgentChange?.(agent);
        localStorage.setItem('AGENT_ID', `${agent?.id}`);
        if (agent) {
            updateAgentConfigMode(agent);
        }
        window.history.replaceState({}, '', `${window.location.pathname}?agentId=${agent?.id}`);
    };

    const initAgentList = async () => {
        const res = await queryAgentList();
        const agentListValue = (res.data || []).filter(
            item => item.status === 1 && (agentIds === undefined || agentIds.includes(item.id))
        );
        setAgentList(agentListValue);
        if (agentListValue.length > 0) {
            const agentId = initialAgentId || localStorage.getItem('AGENT_ID');
            if (agentId) {
                const agent = agentListValue.find(item => item.id === +agentId);
                updateCurrentAgent(agent || agentListValue[0]);
            } else {
                updateCurrentAgent(agentListValue[0]);
            }
        }
    };

    useEffect(() => {
        initAgentList();
    }, []);

    useEffect(() => {
        if (token) {
            setToken(token);
        }
    }, [token]);

    useEffect(() => {
        if (chatVisible) {
            updateMessageContainerScroll();
        }
    }, [chatVisible]);

    useEffect(() => {
        if (!currentConversation) {
            return;
        }
        const {initialMsgParams, isAdd} = currentConversation;
        if (isAdd) {
            if (initialMsgParams) {
                onSendMsg(initialMsgParams.msg, [], initialMsgParams.modelId, initialMsgParams);
                return;
            }
            sendHelloRsp();
            return;
        }
        updateHistoryMsg(1);
        setPageNo(1);
    }, [currentConversation]);

    useEffect(() => {
        if (historyInited) {
            const messageContainerEle = document.getElementById('messageContainer');
            messageContainerEle?.addEventListener('scroll', handleScroll);
        }
        return () => {
            const messageContainerEle = document.getElementById('messageContainer');
            messageContainerEle?.removeEventListener('scroll', handleScroll);
        };
    }, [historyInited]);

    const sendHelloRsp = (agent?: AgentType) => {
        if (noInput) {
            return;
        }
        setMessageList([
            {
                id: uuid(),
                type: MessageTypeEnum.AGENT_LIST,
                msg: agent?.name || currentAgent?.name || agentList?.[0]?.name,
            },
        ]);
    };

    const convertHistoryMsg = (list: HistoryMsgItemType[]) => {
        return list.map((item: HistoryMsgItemType) => ({
            id: item.questionId,
            questionId: item.questionId,
            type: MessageTypeEnum.QUESTION,
            msg: item.queryText,
            parseInfos: item.parseInfos,
            parseTimeCost: item.parseTimeCost,
            msgData: {...(item.queryResult || {}), similarQueries: item.similarQueries},
            score: item.score,
            agentId: currentAgent?.id,
        }));
    };

    const updateHistoryMsg = async (page: number) => {
        const res = await getHistoryMsg(page, currentConversation!.chatId, 3);
        const {hasNextPage, list} = res?.data || {hasNextPage: false, list: []};
        const msgList = [...convertHistoryMsg(list), ...(page === 1 ? [] : messageList)];
        setMessageList(msgList);
        setHasNextPage(hasNextPage);
        if (page === 1) {
            if (list.length === 0) {
                sendHelloRsp();
            }
            updateMessageContainerScroll();
            setHistoryInited(true);
        } else {
            const msgEle = document.getElementById(`${messageList[0]?.id}`);
            msgEle?.scrollIntoView();
        }
    };

    const {run: handleScroll} = useThrottleFn(
        e => {
            if (e.target.scrollTop === 0 && hasNextPage) {
                updateHistoryMsg(pageNo + 1);
                setPageNo(pageNo + 1);
            }
        },
        {
            leading: true,
            trailing: true,
            wait: 200,
        }
    );


    const onSendMsg = async (
        msg?: string,
        list?: MessageItem[],
        modelId?: number,
        sendMsgParams?: SendMsgParamsType
    ) => {
        setLoading(true)
        const currentMsg = msg || "";
        if (currentMsg.trim() === '') {
            return;
        }

        const msgAgent = agentList.find(item => currentMsg.indexOf(item.name) === 1);
        const certainAgent = currentMsg[0] === '/' && msgAgent;
        const agentIdValue = certainAgent ? msgAgent.id : undefined;
        const agent = agentList.find(item => item.id === sendMsgParams?.agentId);

        if (agent || certainAgent) {
            updateCurrentAgent(agent || msgAgent);
        }
        const msgs = [
            ...(list || messageList),
            {
                id: uuid(),
                msg: currentMsg,
                msgValue: certainAgent
                    ? currentMsg.replace(`/${certainAgent.name}`, '').trim()
                    : currentMsg,
                modelId: modelId === -1 ? undefined : modelId,
                agentId: agent?.id || agentIdValue || currentAgent?.id,
                type: MessageTypeEnum.QUESTION,
                filters: sendMsgParams?.filters,
            },
        ];
        setMessageList(msgs);
        setShowChatWelcome(false)
        setLoading(false)
        updateMessageContainerScroll();
    };


    const saveConversationToLocal = (conversation: ConversationDetailType) => {
        if (conversation) {
            if (conversation.chatId !== -1) {
                localStorage.setItem('CONVERSATION_ID', `${conversation.chatId}`);
            }
        } else {
            localStorage.removeItem('CONVERSATION_ID');
        }
    };

    const onSelectConversation = (
        conversation: ConversationDetailType,
        sendMsgParams?: SendMsgParamsType,
        isAdd?: boolean
    ) => {
        setCurrentConversation({
            ...conversation,
            initialMsgParams: sendMsgParams,
            isAdd,
        });
        saveConversationToLocal(conversation);
    };

    const onMsgDataLoaded = (
        data: MsgDataType,
        questionId: string | number,
        question: string,
        valid: boolean,
        isRefresh?: boolean
    ) => {
        onReportMsgEvent?.(question, valid);
        if (!isMobile) {
            conversationRef?.current?.updateData(currentAgent?.id);
        }
        if (!data) {
            return;
        }
        const msgs = cloneDeep(messageList);
        const msg = msgs.find(item => item.id === questionId);
        if (msg) {
            msg.msgData = data;
            setMessageList(msgs);
        }
        if (!isRefresh) {
            updateMessageContainerScroll(`${questionId}`);
        }
    };


    const onAddConversation = () => {
        conversationRef.current?.onAddConversation();
    };

    const onSelectAgent = (agent: AgentType) => {
        if (agent.id === currentAgent?.id) {
            return;
        }
        if (messageList.length === 1 && messageList[0].type === MessageTypeEnum.AGENT_LIST) {
            setMessageList([]);
        }
        updateCurrentAgent(agent);
        updateMessageContainerScroll();
    };

    const sendMsg = (msg: string, modelId?: number) => {
        if (useRag || useNewVersion) {
            setUserInputMsg(msg)
            setShowChatWelcome(false)
        } else {
            onSendMsg(msg, messageList, modelId);
        }
    };

    const onCloseConversation = () => {
        setHistoryVisible(false);
    };

    const onChangeChatPerformanceMode = useCallback((mode) => {
        setChatPerformanceMode(mode);
    }, [])


    const handlerFooterToolsAction = (action: string) => {
        switch (action) {
            case "newChat":
                onAddConversation()
                setShowChatWelcome(true)
                break
            case "chatHistory":
                setHistoryVisible(!historyVisible)
                break
            case "chatPrompts":
                setChatPromptsVisible(!chatPromptsVisible)
                break
            case "changeChatAgent":
                setMobileAgentsVisible(true);
                break
        }
    }

    const handlerMobileChatPromptsPicker = (msg: string) => {
        setChatPromptsVisible(false)
        sendMsg(msg)
    }

    const createMessageContainerDom = () => {
        return currentConversation && (
            <MessageContainer
                id="messageContainer"
                isSimpleMode={true}
                isDebugMode={isDebugMode}
                messageList={messageList}
                //@ts-ignore
                chatId={currentConversation?.chatId}
                currentAgent={currentAgent}
                chatVisible={chatVisible}
                isDeveloper={isDeveloper}
                integrateSystem={integrateSystem}
                onMsgDataLoaded={onMsgDataLoaded}
                onSendMsg={onSendMsg}
                chatPerformanceMode={chatPerformanceMode}
            />
        )
    }

    const createPlanModeMessageContainerDom = () => {
        return currentConversation && (
            <PlanModeMessageContainer
                id="messageContainer"
                isNewChat={showChatWelcome}
                agentId={currentAgent!.id}
                userInputMsg={userInputMsg}
                chatId={currentConversation?.chatId}
                onReviewMsg={(_msg: string) => {
                    setFocusSender(true)
                    setCustomMsg(_msg)
                }}/>
        )
    }

    const createNextChatContainerDom = () => {
        return (<>
            {currentConversation &&
                <NextChatMessageContainer
                    id="messageContainer"
                    isNewChat={showChatWelcome}
                    userInputMsg={userInputMsg}
                    chatId={currentConversation?.chatId}/>
            }
        </>)
    }

    const handlerUseRag = (flag: boolean) => {
        setUseRag(flag)
        if (flag) {
            onAddConversation()
            setShowChatWelcome(true)
        }
    }

    const handlerUseNewVersion = (flag: boolean) => {
        setUseNewVersion(flag)
        if (flag) {
            onAddConversation()
            setShowChatWelcome(true)
        }
    }

    return (
        <ConfigProvider locale={locale}>
            <div className={styles.mobileChatBox}>
                <div className={styles.chatMessageContainer} id="messageContainer">
                    {(showChatWelcome || messageList.length === 0) &&
                        <div className={styles.mobileChatBoxWelcome}>
                            <MobileWelcome example={currentAgent?.examples ? currentAgent?.examples[0] : ""}
                                           agentName={currentAgent?.name || "AI助手"}/>
                        </div>
                    }
                    {!useRag && useNewVersion && createMessageContainerDom()}
                    {useRag && createPlanModeMessageContainerDom()}
                    {useNewVersion && createNextChatContainerDom()}
                </div>
                <div className={styles.mobileChatFooterBox}>
                    <div className={styles.agentBoxFooterTools}>
                        <MobileAgents
                            open={mobileAgentsVisible}
                            agentList={agentList}
                            currentAgent={currentAgent}
                            onSelectAgent={onSelectAgent}
                            onClose={() => {
                                setMobileAgentsVisible(false);
                            }}
                        />
                        <MobileChatPrompts
                            exampleList={currentAgent?.examples || []}
                            chatPromptsVisible={chatPromptsVisible}
                            onChatPromptsClose={() => setChatPromptsVisible(false)}
                            onNewChat={(_content: string) => handlerMobileChatPromptsPicker(_content)}
                        />
                        <MobileVoiceInput voiceInputVisible={voiceInputVisible}
                                          onSendMsg={(_content: string) => sendMsg(_content)}
                                          onCancel={() => setVoiceInputVisible(false)}/>
                        <MobileChatHistory
                            ref={conversationRef}
                            currentAgent={currentAgent}
                            currentConversation={currentConversation}
                            historyVisible={historyVisible}
                            onSelectConversation={onSelectConversation}
                            onCloseConversation={onCloseConversation}
                            onLogout={() => onClickLoginOut()}/>

                        <MobileFooterTools showMobileChatPrompts={showChatWelcome}
                                           onAction={(action) => handlerFooterToolsAction(action)}/>
                    </div>
                    <div className={styles.agentBoxFooterSender}>
                        <MobileChatFooter loading={loading}
                                          customMsg={customMsg}
                                          useIot={false}
                                          showIot={false}
                                          showRag={true}
                                          showNewVersion={true}
                                          focus={focusSender}
                                          useRag={useRag}
                                          useNewVersion={useNewVersion}
                                          onUseRag={(flag: boolean) => handlerUseRag(flag)}
                                          onSubmit={(_content: string) => sendMsg(_content)}
                                          onOpenVoiceInput={() => setVoiceInputVisible(true)}
                                          onChangeChatPerformanceMode={(model: 'speed-first' | 'quality-first') => onChangeChatPerformanceMode(model)}
                                          onUseNewVersion={(flag: boolean) => handlerUseNewVersion(flag)}/>
                    </div>

                </div>
            </div>
        </ConfigProvider>
    );
};

export default forwardRef(MobileChat);
