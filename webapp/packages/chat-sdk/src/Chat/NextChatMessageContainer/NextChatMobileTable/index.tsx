import styles from './style.module.less';
import React, {useEffect, useState} from "react";
import {AgentMessageInfo} from "../data";
import NextChatMobileTableItem from "../NextChatMobileTableItem";
import NextChatMobileTableDrawer from "../NextChatMobileTableDrawer";

type Props = {
    agentMessage: AgentMessageInfo;
};

const NextChatMobileTable: React.FC<Props> = ({
                                                  agentMessage
                                              }) => {

    const [showMoreModel, setShowMoreModel] = useState(false);
    const [tableDataSource, setTableDataSource] = useState<any>([]);
    const [tableColumns, setTableColumns] = useState<{
        title: string
        dataIndex: string
        key: string
    }[]>([]);

    useEffect(() => {
        console.log("表格数据", agentMessage)
        if (agentMessage) {
            initTableColumns()
        }
    }, [agentMessage]);

    const initTableColumns = () => {
        const parseResult = agentMessage.parseResult;
        const columns = parseResult.columns;
        const tmp = columns.map(item => {
            return {
                title: item.description || item.name,
                dataIndex: item.name || item.description,
                key: item.name || item.description,
            }
        })
        setTableColumns(tmp);
        setTableDataSource(agentMessage.data);
    }

    const createMobileTableCardDom = (record: any) => {
        return (
            <>
                <NextChatMobileTableItem
                    record={record}
                    tableColumns={tableColumns}
                    tableDataSourceLength={tableDataSource.length}/>
            </>
        )
    }

    return (
        <>
            <NextChatMobileTableDrawer
                open={showMoreModel}
                tableColumns={tableColumns}
                tableDataSource={tableDataSource}
                onCloseMoreModel={() => setShowMoreModel(false)}/>
            <div className={styles.nextChatMobileTableBox}>
                {tableDataSource.length > 1 &&
                    <div className={styles.mobileTableBoxMoreBtn} onClick={() => setShowMoreModel(true)}>更多</div>
                }
                {tableDataSource.length > 0 && createMobileTableCardDom(tableDataSource[0])}
            </div>
        </>

    );
};

export default NextChatMobileTable;
