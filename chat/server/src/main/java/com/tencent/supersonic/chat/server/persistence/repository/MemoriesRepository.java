package com.tencent.supersonic.chat.server.persistence.repository;

import com.tencent.supersonic.chat.server.persistence.dataobject.MemoriesDO;

import java.util.List;

/**
 * FactualEval
 *
 * <AUTHOR>
 * @date 2025/4/29
 */

public interface MemoriesRepository {

    Long createMemory(MemoriesDO memoriesDO);

    int batchAddMemory(List<MemoriesDO> memoriesDOList);

    List<MemoriesDO> getAllMemory();

    List<MemoriesDO> getMemoriesByChatId(Long chatId);

}
