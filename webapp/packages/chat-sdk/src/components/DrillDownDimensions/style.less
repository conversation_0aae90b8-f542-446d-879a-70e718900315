@import '../../styles/index.less';

@drill-down-dimensions-prefix-cls: ~'@{supersonic-chat-prefix}-drill-down-dimensions';

.@{drill-down-dimensions-prefix-cls} {
  display: flex;
  align-items: center;
  column-gap: 12px;

  &-section {
    // width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    column-gap: 6px;
  }

  &-title {
    color: var(--text-color-third);
  }
  
  &-content {
    display: flex;
    align-items: center;
  }

  &-content-item-name {
    color: var(--chat-blue);
    font-weight: 500;
    border-bottom: 1px solid var(--chat-blue);
    padding: 1px;
    cursor: pointer;
  }

  &-content-item-active {
    color: #fff;
    border-bottom: none;
    background-color: var(--chat-blue);
    border-radius: 2px;
  }

  &-menu-item-active {
    color: var(--chat-blue);
  }

  &-down-arrow {
    color: var(--chat-blue);
  }

  &-cancel-drill-down {
    margin-left: 20px;
    color: var(--text-color-third);
    cursor: pointer;
    padding: 0 4px;
    border: 1px solid var(--text-color-third);
    border-radius: 4px;
    font-size: 12px;

    &:hover {
      color: var(--chat-blue);
      border-color: var(--chat-blue);
    }
  }
}