export const MESSAGE_EVENT_START = "__start__";
export const MESSAGE_EVENT_END = "__END__";
export const MESSAGE_EVENT_MESSAGE = "message";
export const MESSAGE_EVENT_INTERRUPT = "interrupt";
export const MESSAGE_EVENT_NODE_COMPLETE = "node_complete";
export const MESSAGE_EVENT_REASONING = "reasoning";
export const REVIEW_NODE = "review";
export const MESSAGE_FINISH = "[DONE]";
export const PARSE_NODE = "parse_sql_node";
export const EXECUTE_NODE = "execute_sql_node";
export const PARSE_INTENT_NODE = ["guide_rephrase_node", "clarification_generation_node"];

export interface AgentMessageChoiceDelta {
    content: string;
    reasoning_content?: string;
    role: string;
}

export interface AgentMessageChoice {
    finish_reason: string;
    delta: AgentMessageChoiceDelta;
    index: number
}

export interface AgentMessageExtraInfoOption {
    id: string;
    text: string;
    value: string;
}

export interface AgentMessageExtraInfo {
    type: string;
    options: AgentMessageExtraInfoOption[];
    //single、单选和多选
    selectionMode: string;
    helpText: string;
}

export interface AgentMessage {
    connectionId: string;
    threadId: string;
    node: string;
    created: number;
    model: string;
    id: string;
    choices: AgentMessageChoice[];
    object: string;
    extra_info?: AgentMessageExtraInfo;
    response?: any
}

export interface AgentMessageInfo {
    event: string;
    data: AgentMessage
    feedBackSelect?: any
    isFeedBack?: boolean
}

export interface ChatMessage {
    role: 'user' | 'agent',
    chatId: string | number,
    connectionId?: string,
    messageData: string | AgentMessageInfo,
}
