name: supersonic mac CI

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: macos-latest  # Specify a macOS runner

    strategy:
      matrix:
        java-version: [8, 11, 21]  # Define the JDK versions to test

    steps:
      - uses: actions/checkout@v2

      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v2
        with:
          java-version: ${{ matrix.java-version }}
          distribution: 'adopt'

      - name: Cache Maven packages
        uses: actions/cache@v2
        with:
          path: ~/Library/Caches/Maven  # macOS Maven cache path
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build with Maven
        run: mvn -B package --file pom.xml

      - name: Test with Maven
        run: mvn test