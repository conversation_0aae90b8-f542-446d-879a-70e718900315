import React, {useEffect} from 'react';
import {Form, Input, Select} from 'antd';
import TextArea from "antd/es/input/TextArea";
import styles from './style.module.less'

type Props = {
    formData: any
    onChange: (formData: any) => void
};

const SpecialExampleForm: React.FC<Props> = ({
                                                 formData,
                                                 onChange
                                             }) => {

        const [form] = Form.useForm();

        Form.useWatch((values) => {
            console.log(`sql: `,values);
            onChange(values);
        }, form);

        useEffect(() => {
            form.setFieldsValue(formData);
        }, [formData]);


        return (
            <>
                <div className={styles.specialExampleModalBox}>
                    <Form form={form} name="validateOnly" layout="vertical" autoComplete="off">
                        <div>
                            <Form.Item name="name" label="字段" rules={[{required: true}]}>
                                <Input disabled/>
                            </Form.Item>
                            <Form.Item name="dataType" label="字段类型" rules={[{required: true}]}>
                                <Select
                                    options={[
                                        {value: 'string', label: 'String'},
                                        {value: 'int', label: 'Int'},
                                        {value: 'long', label: 'Long'},
                                        {value: 'double', label: 'Double'},
                                        {value: 'float', label: 'Float'},
                                        {value: 'boolean', label: 'Boolean'},
                                        {value: 'short', label: 'Short'},
                                        {value: 'byte', label: 'Byte'},
                                    ]}
                                />
                            </Form.Item>
                            <Form.Item name="comment" label="字段描述">
                                <TextArea/>
                            </Form.Item>
                        </div>
                    </Form>
                </div>
            </>
        );
    }
;

export default SpecialExampleForm;
